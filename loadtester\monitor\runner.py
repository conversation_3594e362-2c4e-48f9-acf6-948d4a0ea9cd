#!/usr/bin/env python3
"""
Integrated Load Test Runner
Combines Locust load testing with backend monitoring for comprehensive analysis
"""

import json
import time
import threading
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

from .backend import BackendMonitor
from ..shared.logger import LoadTestLogger, get_logger, log_error


class IntegratedLoadTestRunner:
    """Runs load tests while collecting backend metrics"""
    
    def __init__(self, output_dir: str = "reports", logs_dir: str = "logs"):
        self.base_output_dir = Path(output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
        self.base_logs_dir = Path(logs_dir)
        self.base_logs_dir.mkdir(exist_ok=True)
        
        # Create datetime-based subdirectory
        self.timestamp = datetime.now()
        self.run_folder = f"run_{self.timestamp.strftime('%Y%m%d_%H%M%S')}"
        self.output_dir = self.base_output_dir / self.run_folder
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize logging with same run ID
        self.logger_system = LoadTestLogger()
        self.logger_system.setup_logging(base_logs_dir=str(self.base_logs_dir), run_id=self.run_folder)
        self.logs_dir = self.logger_system.get_log_directory()
        
        # Get loggers
        self.main_logger = get_logger('main')
        self.backend_logger = get_logger('backend')
        self.error_logger = get_logger('errors')
        
        print(f"Report directory: {self.output_dir}")
        print(f"Logs directory: {self.logs_dir}")
        
        self.monitor = BackendMonitor(output_dir=str(self.output_dir))
        self.locust_process = None
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        
    def start_backend_monitoring(self):
        """Start backend monitoring in a separate thread"""
        self.backend_logger.info("Starting backend monitoring...")
        print("Starting backend monitoring...")
        self.monitoring_thread = threading.Thread(
            target=self._monitor_backend,
            daemon=True
        )
        self.monitoring_thread.start()
        
    def _monitor_backend(self):
        """Background thread for monitoring backend"""
        try:
            self.monitor.start_monitoring()
            seconds = 0
            while not self.stop_monitoring.is_set():
                time.sleep(1)
                seconds += 1
                # Print a simple progress indicator every 10 seconds
                if seconds % 10 == 0:
                    progress_msg = f"Backend monitoring: {seconds}s elapsed..."
                    print(progress_msg)
                    self.backend_logger.info(progress_msg)
            self.monitor.stop_monitoring()
            self.backend_logger.info("Backend monitoring stopped successfully")
        except Exception as e:
            log_error(e, "backend_monitoring", seconds_elapsed=seconds)
            print(f"Backend monitoring error: {e}")
            raise
        
    def run_load_test(
        self,
        users: int = 10,
        spawn_rate: int = 2,
        run_time: str = "30s",
        host: str = "http://localhost:8001",
        headless: bool = True
    ) -> Dict[str, Any]:
        """
        Run integrated load test with backend monitoring
        
        Args:
            users: Number of concurrent users
            spawn_rate: Users spawned per second
            run_time: Test duration (e.g., "30s", "2m")
            host: Target host URL
            headless: Run in headless mode
            
        Returns:
            Combined test results and backend metrics
        """
        
        # Log test start
        test_config = {
            'users': users,
            'spawn_rate': spawn_rate,
            'run_time': run_time,
            'host': host,
            'headless': headless,
            'output_dir': str(self.output_dir),
            'logs_dir': str(self.logs_dir)
        }
        self.logger_system.log_test_start(test_config)
        
        # Start backend monitoring
        self.start_backend_monitoring()
        time.sleep(2)  # Allow monitors to initialize
        
        # Prepare Locust command
        csv_prefix = self.output_dir / "test"
        html_report = self.output_dir / "test.html"
        
        cmd = [
            sys.executable, "-m", "locust",
            "-f", "loadtester/locustfile.py",
            "--host", host,
            "--users", str(users),
            "--spawn-rate", str(spawn_rate),
            "--run-time", run_time,
            "--csv", str(csv_prefix),
            "--html", str(html_report)
        ]
        
        if headless:
            cmd.append("--headless")
            
        cmd_str = ' '.join(cmd)
        print(f"Starting load test: {cmd_str}")
        self.main_logger.info(f"Starting load test: {cmd_str}")
        
        # Get locust logger for streaming output
        locust_logger = get_logger('locust')
        
        # Run Locust
        try:
            self.locust_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Stream output in real-time
            print("Load test progress:")
            locust_logger.info("Load test output streaming started")
            
            while True:
                output = self.locust_process.stdout.readline()
                if output == '' and self.locust_process.poll() is not None:
                    break
                if output:
                    # Log all output to file
                    locust_logger.info(output.strip())
                    
                    # Filter out some noisy lines but keep important ones for console
                    if any(keyword in output for keyword in ['users', 'req/s', 'failures/s', 'Aggregated', 'Shutting down', 'limit reached']):
                        print(f"  {output.strip()}")
            
            # Wait for process to complete
            self.locust_process.wait()
            
            if self.locust_process.returncode != 0:
                error_msg = f"Locust completed with return code: {self.locust_process.returncode}"
                print(error_msg)
                self.main_logger.warning(error_msg)
                locust_logger.error(error_msg)
            else:
                success_msg = "Load test completed successfully!"
                print(success_msg)
                self.main_logger.info(success_msg)
                locust_logger.info(success_msg)
                
        except Exception as e:
            error_msg = f"Error running Locust: {e}"
            print(error_msg)
            log_error(e, "locust_execution", 
                     command=cmd_str,
                     users=users,
                     spawn_rate=spawn_rate,
                     run_time=run_time)
            
        finally:
            # Stop monitoring
            print("Stopping backend monitoring...")
            self.main_logger.info("Stopping backend monitoring...")
            self.stop_monitoring.set()
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5)
                
        # Generate backend analysis
        try:
            self.backend_logger.info("Generating backend analysis report...")
            backend_report = self.monitor.generate_report()
            
            # Save backend report
            backend_report_path = self.output_dir / "backend_metrics.json"
            with open(backend_report_path, 'w', encoding='utf-8') as f:
                json.dump(backend_report, f, indent=2, default=str)
                
            backend_saved_msg = f"Backend metrics saved to: {backend_report_path}"
            print(backend_saved_msg)
            self.backend_logger.info(backend_saved_msg)
            
            # Combine results
            self.main_logger.info("Creating combined analysis report...")
            combined_report = self._create_combined_report(backend_report)
            
            # Save combined report
            combined_report_path = self.output_dir / "combined_analysis.json"
            with open(combined_report_path, 'w', encoding='utf-8') as f:
                json.dump(combined_report, f, indent=2, default=str)
                
            combined_saved_msg = f"Combined analysis saved to: {combined_report_path}"
            print(combined_saved_msg)
            self.main_logger.info(combined_saved_msg)
            
            # Log test completion
            test_results = {
                'backend_report_path': str(backend_report_path),
                'combined_report_path': str(combined_report_path),
                'reports_directory': str(self.output_dir),
                'logs_directory': str(self.logs_dir)
            }
            self.logger_system.log_test_complete(test_results)
            
            return combined_report
            
        except Exception as e:
            log_error(e, "report_generation",
                     output_dir=str(self.output_dir),
                     logs_dir=str(self.logs_dir))
            raise
        
    def _create_combined_report(self, backend_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Create combined report with load test and backend metrics"""
        
        # Load test stats if available
        stats_file = self.output_dir / "test_stats.csv"
        load_test_summary = {}
        
        if stats_file.exists():
            import csv
            with open(stats_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('Name') == 'Aggregated':
                        load_test_summary = {
                            'total_requests': row.get('Request Count', 0),
                            'failure_count': row.get('Failure Count', 0),
                            'median_response_time': row.get('Median Response Time', 0),
                            'average_response_time': row.get('Average Response Time', 0),
                            'min_response_time': row.get('Min Response Time', 0),
                            'max_response_time': row.get('Max Response Time', 0),
                            'requests_per_second': row.get('Requests/s', 0)
                        }
                        break
                        
        # Create scaling insights
        scaling_insights = self._generate_scaling_insights(
            backend_metrics,
            load_test_summary
        )
        
        return {
            'timestamp': datetime.now().isoformat(),
            'load_test_summary': load_test_summary,
            'backend_metrics': backend_metrics,
            'scaling_insights': scaling_insights
        }
        
    def _generate_scaling_insights(
        self,
        backend_metrics: Dict[str, Any],
        load_test_summary: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate scaling insights from combined metrics"""
        
        insights = {
            'resource_utilization': {},
            'bottlenecks': [],
            'recommendations': []
        }
        
        # Analyze container metrics
        if 'containers' in backend_metrics:
            for container in backend_metrics['containers']:
                name = container['name']
                stats = container['statistics']
                
                # Check CPU utilization
                avg_cpu = stats.get('avg_cpu', 0)
                if avg_cpu > 80:
                    insights['bottlenecks'].append({
                        'type': 'cpu',
                        'container': name,
                        'utilization': avg_cpu,
                        'severity': 'high'
                    })
                    insights['recommendations'].append(
                        f"Container '{name}' has high CPU usage ({avg_cpu:.1f}%). "
                        f"Consider scaling horizontally or upgrading CPU resources."
                    )
                    
                # Check memory utilization
                avg_memory_pct = stats.get('avg_memory_percent', 0)
                if avg_memory_pct > 85:
                    insights['bottlenecks'].append({
                        'type': 'memory',
                        'container': name,
                        'utilization': avg_memory_pct,
                        'severity': 'high'
                    })
                    insights['recommendations'].append(
                        f"Container '{name}' has high memory usage ({avg_memory_pct:.1f}%). "
                        f"Consider increasing memory allocation or optimizing memory usage."
                    )
                    
                # Check for memory leaks
                if stats.get('memory_leak_detected', False):
                    insights['bottlenecks'].append({
                        'type': 'memory_leak',
                        'container': name,
                        'growth_rate': stats.get('memory_growth_rate_mb_per_hour', 0),
                        'severity': 'critical'
                    })
                    insights['recommendations'].append(
                        f"Memory leak detected in container '{name}' "
                        f"(growth rate: {stats.get('memory_growth_rate_mb_per_hour', 0):.2f} MB/hour). "
                        f"Investigate and fix memory management issues."
                    )
                    
                insights['resource_utilization'][name] = {
                    'cpu': avg_cpu,
                    'memory': avg_memory_pct,
                    'memory_mb': stats.get('avg_memory_mb', 0)
                }
                
        # Analyze database metrics
        if 'databases' in backend_metrics:
            for db in backend_metrics['databases']:
                connections = db.get('connections', {})
                active = connections.get('active', 0)
                max_conn = connections.get('max_connections', 100)
                utilization = (active / max_conn * 100) if max_conn > 0 else 0
                
                if utilization > 70:
                    insights['bottlenecks'].append({
                        'type': 'database_connections',
                        'database': db['type'],
                        'utilization': utilization,
                        'severity': 'medium' if utilization < 85 else 'high'
                    })
                    insights['recommendations'].append(
                        f"Database connection pool utilization is {utilization:.1f}%. "
                        f"Consider increasing max_connections or implementing connection pooling."
                    )
                    
        # Analyze application KPIs
        if 'application_kpis' in backend_metrics:
            kpis = backend_metrics['application_kpis']
            
            # Check queue depths
            for queue_name, depth in kpis.get('queue_depths', {}).items():
                if depth > 1000:
                    insights['bottlenecks'].append({
                        'type': 'queue_backlog',
                        'queue': queue_name,
                        'depth': depth,
                        'severity': 'medium'
                    })
                    insights['recommendations'].append(
                        f"Queue '{queue_name}' has {depth} pending items. "
                        f"Consider scaling workers or optimizing processing."
                    )
                    
        # Calculate scaling factors based on target capacity
        if load_test_summary:
            current_rps = float(load_test_summary.get('requests_per_second', 1))
            target_users = 2000
            estimated_rps_per_user = 0.5  # Conservative estimate
            target_rps = target_users * estimated_rps_per_user
            scale_factor = target_rps / current_rps if current_rps > 0 else 1
            
            insights['scaling_requirements'] = {
                'current_rps': current_rps,
                'target_rps': target_rps,
                'scale_factor': scale_factor,
                'recommended_instances': max(3, int(scale_factor * 1.3)),  # 30% buffer
                'recommended_cpu_cores': max(4, int(scale_factor * 2)),
                'recommended_memory_gb': max(8, int(scale_factor * 4))
            }
            
        return insights


def main():
    """Main entry point for integrated runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run integrated load test with backend monitoring')
    parser.add_argument('-u', '--users', type=int, default=10, help='Number of concurrent users')
    parser.add_argument('-r', '--spawn-rate', type=int, default=2, help='Spawn rate (users per second)')
    parser.add_argument('-t', '--run-time', default='30s', help='Run time (e.g., 30s, 2m, 1h)')
    parser.add_argument('--host', default='http://localhost:8001', help='Target host URL')
    parser.add_argument('--output', default='reports', help='Output directory for reports')
    parser.add_argument('--no-headless', action='store_true', help='Run with web UI')
    
    args = parser.parse_args()
    
    runner = IntegratedLoadTestRunner(output_dir=args.output)
    
    print("Starting integrated load test with backend monitoring...")
    results = runner.run_load_test(
        users=args.users,
        spawn_rate=args.spawn_rate,
        run_time=args.run_time,
        host=args.host,
        headless=not args.no_headless
    )
    
    print("\nTest completed. Summary:")
    print(f"- Load test results: {args.output}/test_stats.csv")
    print(f"- Backend metrics: {args.output}/backend_metrics.json")
    print(f"- Combined analysis: {args.output}/combined_analysis.json")
    print(f"- HTML report: {args.output}/test.html")
    
    # Print key insights
    if 'scaling_insights' in results:
        insights = results['scaling_insights']
        if 'bottlenecks' in insights and insights['bottlenecks']:
            print("\nBottlenecks detected:")
            for bottleneck in insights['bottlenecks']:
                print(f"  - {bottleneck['type']}: {bottleneck.get('severity', 'unknown')} severity")
                
        if 'recommendations' in insights and insights['recommendations']:
            print("\nRecommendations:")
            for rec in insights['recommendations'][:3]:  # Show top 3
                print(f"  - {rec}")
                
        if 'scaling_requirements' in insights:
            req = insights['scaling_requirements']
            print("\nScaling requirements for 2000 users:")
            print(f"  - Recommended instances: {req['recommended_instances']}")
            print(f"  - Recommended CPU cores: {req['recommended_cpu_cores']}")
            print(f"  - Recommended memory: {req['recommended_memory_gb']} GB")


if __name__ == '__main__':
    main()