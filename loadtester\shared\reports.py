"""
Shared report generation utilities
"""

import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime


class ReportGenerator:
    """Base report generator with common functionality"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def save_json(self, data: Dict[str, Any], filename: str) -> Path:
        """Save data as JSON file"""
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
        return output_path
    
    def load_json(self, filename: str) -> Dict[str, Any]:
        """Load JSON file"""
        file_path = self.output_dir / filename
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_html(self, content: str, filename: str) -> Path:
        """Save HTML content to file"""
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return output_path
    
    def save_markdown(self, content: str, filename: str) -> Path:
        """Save markdown content to file"""
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return output_path


class ReportFormatter:
    """Format reports in various output formats"""
    
    @staticmethod
    def format_currency(amount: float) -> str:
        """Format currency value"""
        return f"${amount:,.0f}"
    
    @staticmethod
    def format_percentage(value: float, decimals: int = 1) -> str:
        """Format percentage value"""
        return f"{value:.{decimals}f}%"
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """Format duration in human-readable form"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    @staticmethod
    def format_bytes(bytes_value: float) -> str:
        """Format bytes in human-readable form"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"
    
    @staticmethod
    def create_html_table(headers: List[str], rows: List[List[Any]]) -> str:
        """Create HTML table"""
        html = '<table class="data-table">\n<thead>\n<tr>\n'
        for header in headers:
            html += f'<th>{header}</th>\n'
        html += '</tr>\n</thead>\n<tbody>\n'
        
        for row in rows:
            html += '<tr>\n'
            for cell in row:
                html += f'<td>{cell}</td>\n'
            html += '</tr>\n'
        
        html += '</tbody>\n</table>\n'
        return html
    
    @staticmethod
    def create_markdown_table(headers: List[str], rows: List[List[Any]]) -> str:
        """Create markdown table"""
        md = '| ' + ' | '.join(headers) + ' |\n'
        md += '|' + '---|' * len(headers) + '\n'
        
        for row in rows:
            md += '| ' + ' | '.join(str(cell) for cell in row) + ' |\n'
        
        return md
    
    @staticmethod
    def get_status_badge(status: str) -> str:
        """Get HTML status badge"""
        colors = {
            'GOOD': '#4CAF50',
            'WARNING': '#FFC107',
            'NEEDS_IMPROVEMENT': '#FF9800',
            'CRITICAL': '#F44336',
            'NEEDS_IMMEDIATE_ATTENTION': '#F44336'
        }
        color = colors.get(status, '#9E9E9E')
        return f'<span style="background:{color};color:white;padding:4px 8px;border-radius:4px;font-weight:bold;">{status}</span>'
    
    @staticmethod
    def get_priority_color(priority: str) -> str:
        """Get color for priority level"""
        colors = {
            'critical': '#F44336',
            'high': '#FF9800',
            'medium': '#FFC107',
            'low': '#4CAF50'
        }
        return colors.get(priority.lower(), '#9E9E9E')
    
    def generate_base_html(self, title: str, content: str) -> str:
        """Generate base HTML template"""
        return f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        h1 {{
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        .metric-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .metric-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }}
        .metric-label {{
            color: #7f8c8d;
            margin-top: 5px;
        }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .data-table th {{
            background: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
        }}
        .data-table td {{
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }}
        .data-table tr:hover {{
            background: #f5f5f5;
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .recommendation {{
            background: #fff;
            border-left: 4px solid;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }}
        .priority-critical {{
            border-color: #F44336;
            background: #ffebee;
        }}
        .priority-high {{
            border-color: #FF9800;
            background: #fff3e0;
        }}
        .priority-medium {{
            border-color: #FFC107;
            background: #fffde7;
        }}
        .priority-low {{
            border-color: #4CAF50;
            background: #e8f5e9;
        }}
        .timestamp {{
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }}
        code {{
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        pre {{
            background: #f4f4f4;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }}
    </style>
</head>
<body>
    {content}
    <div class="timestamp">
        Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    </div>
</body>
</html>
"""