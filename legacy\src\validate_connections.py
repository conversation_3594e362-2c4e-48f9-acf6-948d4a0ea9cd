async def validate_database_connections():
    """Check if database can handle concurrent connections"""
    max_connections = 100
    tasks = []
    
    for i in range(max_connections):
        task = asyncio.create_task(test_db_connection(i))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = sum(1 for r in results if not isinstance(r, Exception))
    print(f"Database connections: {successful}/{max_connections} successful")