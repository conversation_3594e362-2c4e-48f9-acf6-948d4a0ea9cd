#!/usr/bin/env python3
"""
Configuration Management Script
==============================

Manages load test configurations, validates settings, and provides
utilities for configuration manipulation and environment setup.
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from locust_framework import ConfigManager, JWTValidator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Main configuration management function"""
    parser = argparse.ArgumentParser(
        description="Load Test Configuration Management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Validate all configurations
  python scripts/manage_config.py --validate-all
  
  # Create new context configuration
  python scripts/manage_config.py --create-context myapp --base-url https://api.myapp.com
  
  # Update JWT token in global config
  python scripts/manage_config.py --update-jwt "new_jwt_token_here"
  
  # Show merged configuration for context
  python scripts/manage_config.py --show-config --context andoc --environment production
  
  # Validate JWT token
  python scripts/manage_config.py --validate-jwt
        """
    )
    
    # Configuration actions
    parser.add_argument("--validate-all", action="store_true", help="Validate all configurations")
    parser.add_argument("--validate-jwt", action="store_true", help="Validate JWT token")
    parser.add_argument("--show-config", action="store_true", help="Show merged configuration")
    parser.add_argument("--create-context", type=str, help="Create new context configuration")
    parser.add_argument("--update-jwt", type=str, help="Update JWT token in global config")
    parser.add_argument("--list-contexts", action="store_true", help="List available contexts")
    parser.add_argument("--list-environments", action="store_true", help="List available environments")
    
    # Configuration parameters
    parser.add_argument("--context", type=str, help="Context name")
    parser.add_argument("--environment", type=str, help="Environment name")
    parser.add_argument("--base-url", type=str, help="Base URL for new context")
    parser.add_argument("--num-users", type=int, help="Number of users for new context")
    parser.add_argument("--output", type=str, help="Output file for configuration")
    
    # Options
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        config_manager = ConfigManager()
        
        if args.validate_all:
            validate_all_configurations(config_manager)
        elif args.validate_jwt:
            validate_jwt_token(config_manager)
        elif args.show_config:
            show_merged_config(config_manager, args.context, args.environment)
        elif args.create_context:
            create_context_config(config_manager, args.create_context, args.base_url, args.num_users)
        elif args.update_jwt:
            update_jwt_token(config_manager, args.update_jwt)
        elif args.list_contexts:
            list_contexts(config_manager)
        elif args.list_environments:
            list_environments(config_manager)
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def validate_all_configurations(config_manager: ConfigManager):
    """Validate all configuration files"""
    logger.info("🔍 Validating all configurations...")
    
    errors = []
    
    # Validate global config
    try:
        global_config = config_manager.load_config()
        validation_errors = config_manager.validate_config(global_config)
        if validation_errors:
            errors.extend([f"Global config: {error}" for error in validation_errors])
        else:
            logger.info("✅ Global configuration is valid")
    except Exception as e:
        errors.append(f"Global config: {str(e)}")
    
    # Validate context configs
    contexts = list_contexts(config_manager, silent=True)
    for context in contexts:
        try:
            context_config = config_manager.load_config(context=context)
            validation_errors = config_manager.validate_config(context_config)
            if validation_errors:
                errors.extend([f"Context '{context}': {error}" for error in validation_errors])
            else:
                logger.info(f"✅ Context '{context}' configuration is valid")
        except Exception as e:
            errors.append(f"Context '{context}': {str(e)}")
    
    # Validate environment configs
    environments = list_environments(config_manager, silent=True)
    for env in environments:
        try:
            env_config = config_manager.load_config(environment=env)
            validation_errors = config_manager.validate_config(env_config)
            if validation_errors:
                errors.extend([f"Environment '{env}': {error}" for error in validation_errors])
            else:
                logger.info(f"✅ Environment '{env}' configuration is valid")
        except Exception as e:
            errors.append(f"Environment '{env}': {str(e)}")
    
    if errors:
        logger.error("❌ Configuration validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        sys.exit(1)
    else:
        logger.info("🎉 All configurations are valid!")


def validate_jwt_token(config_manager: ConfigManager):
    """Validate JWT token"""
    logger.info("🔐 Validating JWT token...")
    
    try:
        config = config_manager.load_config()
        token_info = JWTValidator.get_token_info(config.jwt_token)
        
        print("\n📋 JWT Token Information:")
        print(f"  Valid: {'✅' if token_info['valid'] else '❌'}")
        print(f"  Expired: {'❌' if token_info['expired'] else '✅'}")
        print(f"  User ID: {token_info.get('user_id', 'N/A')}")
        print(f"  Name: {token_info.get('name', 'N/A')}")
        print(f"  Expires At: {token_info.get('expires_at', 'N/A')}")
        print(f"  Expires In: {token_info.get('expires_in_hours', 0):.1f} hours")
        print(f"  Permissions: {len(token_info.get('permissions', []))} permissions")
        print(f"  Groups: {len(token_info.get('groups', []))} groups")
        
        if token_info['expired']:
            logger.error("❌ JWT token has expired!")
            sys.exit(1)
        elif token_info.get('expires_in_hours', 0) < 1:
            logger.warning("⚠️  JWT token expires in less than 1 hour!")
        else:
            logger.info("✅ JWT token is valid and not expired")
            
    except Exception as e:
        logger.error(f"❌ Error validating JWT token: {e}")
        sys.exit(1)


def show_merged_config(config_manager: ConfigManager, context: Optional[str], environment: Optional[str]):
    """Show merged configuration"""
    logger.info(f"📋 Showing merged configuration (context: {context}, environment: {environment})")
    
    try:
        config = config_manager.load_config(context=context, environment=environment)
        
        # Convert to dictionary for display
        config_dict = {
            'base_url': config.base_url,
            'jwt_token': config.jwt_token[:20] + "..." if config.jwt_token else None,
            'verify_ssl': config.verify_ssl,
            'num_users': config.num_users,
            'spawn_rate': config.spawn_rate,
            'run_time': config.run_time,
            'connection_timeout': config.connection_timeout,
            'read_timeout': config.read_timeout,
            'max_retries': config.max_retries,
            'wait_time_min': config.wait_time_min,
            'wait_time_max': config.wait_time_max,
            'endpoints': config.endpoints,
            'enable_monitoring': config.enable_monitoring,
            'report_format': config.report_format,
            'report_dir': config.report_dir,
            'custom_headers': config.custom_headers,
            'test_data': config.test_data
        }
        
        print("\n📄 Merged Configuration:")
        print(json.dumps(config_dict, indent=2, ensure_ascii=False))
        
    except Exception as e:
        logger.error(f"❌ Error loading configuration: {e}")
        sys.exit(1)


def create_context_config(config_manager: ConfigManager, context_name: str, 
                         base_url: Optional[str], num_users: Optional[int]):
    """Create new context configuration"""
    logger.info(f"🆕 Creating context configuration: {context_name}")
    
    if not base_url:
        base_url = input("Enter base URL: ")
    
    if not num_users:
        num_users = int(input("Enter number of users (default 10): ") or "10")
    
    # Create basic context configuration
    context_config = {
        "base_url": base_url,
        "num_users": num_users,
        "endpoints": [
            {
                "url": "/health",
                "method": "GET",
                "id": "health_check"
            }
        ]
    }
    
    # Save to file
    config_file = config_manager.configs_dir / f"{context_name}.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(context_config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ Context configuration created: {config_file}")
    print(f"\n📄 Created configuration:")
    print(json.dumps(context_config, indent=2, ensure_ascii=False))


def update_jwt_token(config_manager: ConfigManager, new_token: str):
    """Update JWT token in global configuration"""
    logger.info("🔄 Updating JWT token in global configuration...")
    
    try:
        # Load current global config
        with open(config_manager.global_config_path, 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        
        # Update token
        global_config['jwt_token'] = new_token
        
        # Save updated config
        with open(config_manager.global_config_path, 'w', encoding='utf-8') as f:
            json.dump(global_config, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ JWT token updated successfully")
        
        # Validate new token
        token_info = JWTValidator.get_token_info(new_token)
        if token_info['valid'] and not token_info['expired']:
            logger.info("✅ New JWT token is valid")
        else:
            logger.warning("⚠️  New JWT token may be invalid or expired")
            
    except Exception as e:
        logger.error(f"❌ Error updating JWT token: {e}")
        sys.exit(1)


def list_contexts(config_manager: ConfigManager, silent: bool = False) -> List[str]:
    """List available contexts"""
    if not silent:
        logger.info("📋 Available contexts:")
    
    contexts = []
    
    if config_manager.configs_dir.exists():
        for config_file in config_manager.configs_dir.glob("*.json"):
            if config_file.name != "environments.json":
                context_name = config_file.stem
                contexts.append(context_name)
                if not silent:
                    print(f"  - {context_name}")
    
    if not contexts and not silent:
        print("  No contexts found")
    
    return contexts


def list_environments(config_manager: ConfigManager, silent: bool = False) -> List[str]:
    """List available environments"""
    if not silent:
        logger.info("🌍 Available environments:")
    
    environments = []
    env_file = config_manager.configs_dir / "environments.json"
    
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                env_config = json.load(f)
            
            environments = list(env_config.keys())
            if not silent:
                for env_name in environments:
                    print(f"  - {env_name}")
        except Exception as e:
            if not silent:
                logger.error(f"Error reading environments file: {e}")
    
    if not environments and not silent:
        print("  No environments found")
    
    return environments


if __name__ == "__main__":
    main()
