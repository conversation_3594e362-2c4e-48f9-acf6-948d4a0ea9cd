#!/usr/bin/env python3
"""
Failover test - Test behavior when AOAI or other services are unavailable
"""
import asyncio
import httpx
import json
import time
from datetime import datetime
import argparse

class FailoverTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]
        self.jwt_token = self.config["jwt_token"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}

    async def run_failover_tests(self):
        """Run failover scenario tests"""
        print("🔀 Starting failover tests...")
        
        tests = [
            ("AOAI Service Unavailable", self._test_aoai_failover),
            ("Database Connection Lost", self._test_database_failover),
            ("Authentication Service Down", self._test_auth_failover),
            ("Partial Service Degradation", self._test_partial_degradation)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            result = await test_func()
            result["test_name"] = test_name
            results.append(result)
        
        await self._save_failover_results(results)

    async def _test_aoai_failover(self):
        """Test behavior when AOAI is unavailable"""
        print("   Testing AOAI failover scenarios...")
        
        # Test endpoints that depend on AOAI
        aoai_endpoints = [
            "/andoc/chat/send",
            "/talperftraitement/rag/message"
        ]
        
        results = {}
        
        for endpoint in aoai_endpoints:
            print(f"   Testing {endpoint}...")
            
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        f"{self.base_url}{endpoint}",
                        headers=self.headers,
                        json={"message": "Test message", "session_id": "test_session"}
                    )
                    
                    results[endpoint] = {
                        "status_code": response.status_code,
                        "response_time": response.elapsed.total_seconds(),
                        "has_error_handling": response.status_code in [503, 502, 500],
                        "graceful_degradation": response.status_code == 200 or "error" in response.text.lower()
                    }
                    
            except asyncio.TimeoutError:
                results[endpoint] = {
                    "status_code": "timeout",
                    "response_time": 30.0,
                    "has_error_handling": False,
                    "graceful_degradation": False
                }
            except Exception as e:
                results[endpoint] = {
                    "status_code": "exception",
                    "error": str(e),
                    "has_error_handling": False,
                    "graceful_degradation": False
                }
        
        return {"endpoint_results": results}

    async def _test_database_failover(self):
        """Test database connection failover"""
        print("   Testing database failover...")
        
        # Test endpoints that require database
        db_endpoints = [
            "/andoc/session/new",
            "/talperftraitement/session/new"
        ]
        
        results = {}
        
        for endpoint in db_endpoints:
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.post(
                        f"{self.base_url}{endpoint}",
                        headers=self.headers
                    )
                    
                    results[endpoint] = {
                        "status_code": response.status_code,
                        "response_time": response.elapsed.total_seconds(),
                        "creates_session": "session_id" in response.text
                    }
                    
            except Exception as e:
                results[endpoint] = {
                    "status_code": "exception",
                    "error": str(e),
                    "creates_session": False
                }
        
        return {"database_results": results}

    async def _test_auth_failover(self):
        """Test authentication service failover"""
        print("   Testing authentication failover...")
        
        # Test with various auth scenarios
        auth_tests = [
            ("valid_token", self.headers),
            ("invalid_token", {"X-CGPT-AUTHORIZATION": "Bearer invalid_token"}),
            ("missing_token", {}),
            ("malformed_token", {"X-CGPT-AUTHORIZATION": "malformed"})
        ]
        
        results = {}
        
        for test_name, headers in auth_tests:
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(
                        f"{self.base_url}/health",
                        headers=headers
                    )
                    
                    results[test_name] = {
                        "status_code": response.status_code,
                        "response_time": response.elapsed.total_seconds(),
                        "proper_auth_handling": response.status_code in [200, 401, 403]
                    }
                    
            except Exception as e:
                results[test_name] = {
                    "status_code": "exception",
                    "error": str(e),
                    "proper_auth_handling": False
                }
        
        return {"auth_results": results}

    async def _test_partial_degradation(self):
        """Test partial service degradation"""
        print("   Testing partial service degradation...")
        
        # Test multiple endpoints simultaneously
        endpoints = [
            "/health",
            "/andoc/session/new",
            "/talperftraitement/session/new"
        ]
        
        # Run concurrent requests to simulate load during degradation
        tasks = []
        for endpoint in endpoints:
            for _ in range(10):  # 10 requests per endpoint
                task = asyncio.create_task(self._make_request(endpoint))
                tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
        failed = len(results) - successful
        
        return {
            "total_requests": len(results),
            "successful": successful,
            "failed": failed,
            "success_rate": (successful / len(results)) * 100,
            "total_time": total_time,
            "avg_response_time": total_time / len(results)
        }

    async def _make_request(self, endpoint: str):
        """Make a single request for testing"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                if endpoint.endswith("/new"):
                    response = await client.post(f"{self.base_url}{endpoint}", headers=self.headers)
                else:
                    response = await client.get(f"{self.base_url}{endpoint}", headers=self.headers)
                
                return {
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "response_time": response.elapsed.total_seconds(),
                    "success": response.status_code < 400
                }
        except Exception as e:
            return {
                "endpoint": endpoint,
                "error": str(e),
                "success": False
            }

    async def _save_failover_results(self, results: list):
        """Save failover test results"""
        filename = f"failover_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "test_type": "failover_test",
            "test_date": datetime.now().isoformat(),
            "results": results
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 Failover test results saved to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Failover scenario testing")
    parser.add_argument("--config", default="config.global.json", help="Configuration file")
    
    args = parser.parse_args()
    
    tester = FailoverTester(args.config)
    await tester.run_failover_tests()

if __name__ == "__main__":
    asyncio.run(main())