"""
Unit tests for the main module.
"""
import pytest
import sys
from pathlib import Path

# Add parent to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.main import TestMode


class TestMain:
    """Test the main module functionality."""
    
    def test_test_mode_enum(self):
        """Test that TestMode enum has correct values."""
        assert TestMode.ASYNCIO.value == "asyncio"
        assert TestMode.LOCUST.value == "locust"
    
    def test_test_mode_members(self):
        """Test that TestMode has expected members."""
        members = [mode.value for mode in TestMode]
        assert "asyncio" in members
        assert "locust" in members
        assert len(members) == 2