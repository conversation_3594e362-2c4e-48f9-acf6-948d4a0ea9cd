global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # FastAPI metrics
  - job_name: 'fastapi'
    static_configs:
      - targets: ['api:8001']
    metrics_path: '/api/metrics'

  # PostgreSQL exporter (if added later)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis exporter (if added later)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']