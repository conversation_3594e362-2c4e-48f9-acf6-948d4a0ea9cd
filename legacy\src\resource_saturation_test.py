#!/usr/bin/env python3
"""
Resource Saturation Test - Push CPU, RAM, and I/O to limits
"""
import asyncio
import psutil
import json
import subprocess
import time
from datetime import datetime

class ResourceSaturationTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)

    async def run_saturation_tests(self):
        """Test resource saturation scenarios"""
        print("💾 Starting resource saturation tests...")
        
        tests = [
            ("CPU Saturation", self._test_cpu_saturation),
            ("Memory Saturation", self._test_memory_saturation),
            ("Network I/O Saturation", self._test_network_saturation),
            ("Combined Resource Stress", self._test_combined_stress)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Testing {test_name}...")
            result = await test_func()
            results.append({"test_name": test_name, **result})
            
            # Cool down between tests
            await asyncio.sleep(30)
        
        await self._save_saturation_results(results)

    async def _test_cpu_saturation(self):
        """Test behavior under high CPU load"""
        print("   Pushing CPU to 90%+ utilization...")
        
        # Monitor baseline
        baseline_cpu = psutil.cpu_percent(interval=1)
        
        # Create high CPU load config
        cpu_config = self.config.copy()
        cpu_config['num_threads'] = psutil.cpu_count() * 50  # Oversubscribe CPU
        
        temp_config = "temp_cpu_saturation.json"
        with open(temp_config, 'w') as f:
            json.dump(cpu_config, f, indent=2)
        
        try:
            # Start load test
            process = subprocess.Popen([
                "python", "-m", "src.main",
                "-c", temp_config,
                "--skip-jwt-validation"
            ])
            
            # Monitor CPU for 60 seconds
            cpu_samples = []
            for _ in range(12):  # 12 samples over 60 seconds
                await asyncio.sleep(5)
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_samples.append(cpu_percent)
                print(f"   CPU: {cpu_percent:.1f}%")
            
            process.terminate()
            process.wait(timeout=10)
            
            return {
                "baseline_cpu": baseline_cpu,
                "max_cpu": max(cpu_samples),
                "avg_cpu": sum(cpu_samples) / len(cpu_samples),
                "cpu_samples": cpu_samples
            }
            
        finally:
            import os
            if os.path.exists(temp_config):
                os.remove(temp_config)

if __name__ == "__main__":
    asyncio.run(ResourceSaturationTester().run_saturation_tests())