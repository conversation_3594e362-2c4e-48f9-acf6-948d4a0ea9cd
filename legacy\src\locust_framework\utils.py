"""
Utility classes and functions for the Locust Load Testing Framework
==================================================================

Provides JWT validation, session management, and other utility functions.
"""

import jwt
import json
import time
import httpx
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class SessionData:
    """Container for user session data"""
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    created_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    context_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context_data is None:
            self.context_data = {}
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)


class JWTValidator:
    """Validates JWT tokens and extracts user information"""
    
    @staticmethod
    def decode_token(token: str, verify: bool = False) -> Dict[str, Any]:
        """
        Decode JWT token without verification (for inspection)
        
        Args:
            token: JWT token string
            verify: Whether to verify signature (requires secret)
            
        Returns:
            Dict containing token payload
        """
        try:
            # Remove 'Bearer ' prefix if present
            if token.startswith('Bearer '):
                token = token[7:]
            
            if verify:
                # Note: In production, you'd need the secret key
                decoded = jwt.decode(token, options={"verify_signature": False})
            else:
                decoded = jwt.decode(token, options={"verify_signature": False})
            
            return decoded
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid JWT token: {e}")
            return {}
    
    @staticmethod
    def is_token_expired(token: str) -> bool:
        """Check if JWT token is expired"""
        try:
            payload = JWTValidator.decode_token(token)
            if 'exp' in payload:
                exp_timestamp = payload['exp']
                current_timestamp = time.time()
                return current_timestamp > exp_timestamp
            return False
        except Exception as e:
            logger.error(f"Error checking token expiration: {e}")
            return True
    
    @staticmethod
    def get_token_info(token: str) -> Dict[str, Any]:
        """Extract useful information from JWT token"""
        payload = JWTValidator.decode_token(token)
        
        info = {
            'valid': bool(payload),
            'expired': JWTValidator.is_token_expired(token),
            'user_id': payload.get('sub'),
            'name': payload.get('name'),
            'permissions': payload.get('cgpt_permissions', []),
            'groups': payload.get('talperftraitement_groups', [])
        }
        
        if 'exp' in payload:
            exp_datetime = datetime.fromtimestamp(payload['exp'], tz=timezone.utc)
            info['expires_at'] = exp_datetime.isoformat()
            info['expires_in_hours'] = (exp_datetime - datetime.now(timezone.utc)).total_seconds() / 3600
        
        return info
    
    @staticmethod
    async def validate_token_with_server(base_url: str, token: str, 
                                       verify_ssl: bool = True) -> Tuple[bool, str]:
        """
        Validate JWT token by making a test request to the server
        
        Args:
            base_url: API base URL
            token: JWT token
            verify_ssl: Whether to verify SSL certificates
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            headers = {"X-CGPT-AUTHORIZATION": f"Bearer {token}"}
            
            async with httpx.AsyncClient(verify=verify_ssl, timeout=10.0) as client:
                response = await client.get(f"{base_url}/health", headers=headers)
                
                if response.status_code == 200:
                    return True, "✅ JWT token is valid and accepted by server"
                elif response.status_code == 401:
                    return False, "❌ JWT token is invalid or expired"
                else:
                    return False, f"⚠️ Server returned status {response.status_code}"
                    
        except httpx.TimeoutException:
            return False, "⚠️ Timeout validating token with server"
        except Exception as e:
            return False, f"⚠️ Error validating token: {str(e)}"


class SessionManager:
    """Manages user sessions for load testing"""
    
    def __init__(self, base_url: str, jwt_token: str, verify_ssl: bool = True):
        self.base_url = base_url
        self.jwt_token = jwt_token
        self.verify_ssl = verify_ssl
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {jwt_token}"}
        self.session_data = SessionData()
    
    async def create_session(self, context: str = "andoc") -> bool:
        """
        Create a new session for the specified context
        
        Args:
            context: Context name (e.g., 'andoc', 'talperftraitement')
            
        Returns:
            bool: True if session created successfully
        """
        endpoint = f"/{context}/session/new"
        
        try:
            async with httpx.AsyncClient(verify=self.verify_ssl, timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}{endpoint}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    self.session_data.session_id = data.get("session_id")
                    self.session_data.context_data["context"] = context
                    self.session_data.last_activity = datetime.now(timezone.utc)
                    
                    logger.info(f"Session created for context {context}: {self.session_data.session_id}")
                    return True
                else:
                    logger.error(f"Failed to create session: HTTP {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return False
    
    def get_session_headers(self) -> Dict[str, str]:
        """Get headers including session information"""
        headers = self.headers.copy()
        if self.session_data.session_id:
            headers["X-Session-ID"] = self.session_data.session_id
        return headers
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.session_data.last_activity = datetime.now(timezone.utc)
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get session information for reporting"""
        return {
            "session_id": self.session_data.session_id,
            "created_at": self.session_data.created_at.isoformat() if self.session_data.created_at else None,
            "last_activity": self.session_data.last_activity.isoformat() if self.session_data.last_activity else None,
            "context": self.session_data.context_data.get("context"),
            "active_duration_minutes": (
                (datetime.now(timezone.utc) - self.session_data.created_at).total_seconds() / 60
                if self.session_data.created_at else 0
            )
        }


class RequestTracker:
    """Tracks request patterns and performance"""
    
    def __init__(self):
        self.request_history = []
        self.error_patterns = {}
        self.slow_requests = []
    
    def record_request(self, method: str, url: str, response_time: float, 
                      status_code: int, error: Optional[str] = None):
        """Record a request for analysis"""
        request_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "method": method,
            "url": url,
            "response_time": response_time,
            "status_code": status_code,
            "error": error
        }
        
        self.request_history.append(request_data)
        
        # Track error patterns
        if error or status_code >= 400:
            error_key = f"{status_code}_{error or 'HTTP_ERROR'}"
            self.error_patterns[error_key] = self.error_patterns.get(error_key, 0) + 1
        
        # Track slow requests (>5 seconds)
        if response_time > 5000:
            self.slow_requests.append(request_data)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get request statistics"""
        if not self.request_history:
            return {}
        
        response_times = [req["response_time"] for req in self.request_history]
        status_codes = [req["status_code"] for req in self.request_history]
        
        return {
            "total_requests": len(self.request_history),
            "avg_response_time": sum(response_times) / len(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "error_rate": len([s for s in status_codes if s >= 400]) / len(status_codes),
            "slow_requests_count": len(self.slow_requests),
            "error_patterns": self.error_patterns
        }


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    else:
        return f"{seconds/3600:.1f}h"


def format_bytes(bytes_count: int) -> str:
    """Format bytes in human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_count < 1024:
            return f"{bytes_count:.1f}{unit}"
        bytes_count /= 1024
    return f"{bytes_count:.1f}TB"
