param(
  [Parameter(Mandatory=$true)]
  [string]$context
)

# Vérifie et active l'environnement virtuel .venv si nécessaire
$venvPath = ".venv"
$venvActivate = Join-Path $venvPath "Scripts\Activate.ps1"

if (Test-Path $venvActivate) {
    # Vérifie si l'environnement est déjà activé
    if (-not $env:VIRTUAL_ENV) {
        Write-Host "Activation de l'environnement virtuel .venv..."
        & $venvActivate
    }
}

$contextConfig = "configs/$context.json"


# Lance le test Python en passant le chemin du contexte
python src/main.py -c $contextConfig
