#!/usr/bin/env python3
"""
Long-Duration Soak Test - Run moderate load for hours to detect slow degradation
"""
import asyncio
import subprocess
import json
import time
import psutil
from datetime import datetime, timedelta

class SoakTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.metrics_history = []

    async def run_soak_test(self, duration_hours: int = 4, users: int = 200):
        """Run sustained load test for extended period"""
        print(f"🕐 Starting {duration_hours}h soak test with {users} users...")
        
        # Create soak test config
        soak_config = self.config.copy()
        soak_config['num_threads'] = users
        soak_config['test_duration'] = duration_hours * 3600  # Convert to seconds
        
        temp_config = "temp_soak_test.json"
        with open(temp_config, 'w') as f:
            json.dump(soak_config, f, indent=2)
        
        try:
            # Start monitoring task
            monitor_task = asyncio.create_task(self._monitor_soak_metrics(duration_hours * 3600))
            
            # Start load test
            load_task = asyncio.create_task(self._run_soak_load(temp_config))
            
            # Wait for completion
            await asyncio.gather(monitor_task, load_task)
            
            # Analyze degradation
            await self._analyze_degradation()
            
        finally:
            import os
            if os.path.exists(temp_config):
                os.remove(temp_config)

    async def _monitor_soak_metrics(self, duration_seconds: int):
        """Monitor system metrics throughout soak test"""
        start_time = time.time()
        sample_interval = 60  # Sample every minute
        
        while (time.time() - start_time) < duration_seconds:
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "elapsed_hours": (time.time() - start_time) / 3600,
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "memory_available_gb": psutil.virtual_memory().available / (1024**3),
                "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                "network_io": psutil.net_io_counters()._asdict()
            }
            
            self.metrics_history.append(metrics)
            
            # Log every hour
            if len(self.metrics_history) % 60 == 0:
                hours = len(self.metrics_history) // 60
                print(f"   Hour {hours}: CPU {metrics['cpu_percent']:.1f}%, RAM {metrics['memory_percent']:.1f}%")
            
            await asyncio.sleep(sample_interval)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Long-duration soak test")
    parser.add_argument("--hours", type=int, default=4, help="Test duration in hours")
    parser.add_argument("--users", type=int, default=200, help="Number of concurrent users")
    
    args = parser.parse_args()
    asyncio.run(SoakTester().run_soak_test(args.hours, args.users))