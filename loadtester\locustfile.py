"""
Locust Load Testing Framework
Simplified version with modular design
"""

import random
import time
import os
import logging
from pathlib import Path

from locust import HttpUser, task, between, events


# Setup logger
logger = logging.getLogger(__name__)

# Try to import shared config
HAS_CONFIG = False
Config = None

try:
    from .shared.config import Config
    HAS_CONFIG = True
except ImportError:
    try:
        from shared.config import Config
        HAS_CONFIG = True
    except ImportError:
        try:
            from loadtester.shared.config import Config
            HAS_CONFIG = True
        except ImportError:
            HAS_CONFIG = False
            Config = None
            logger.info("Config module not available, using defaults")

# Configuration from environment
HOST = os.environ.get("HOST", "http://localhost:8001")
CONFIG_FILE = os.environ.get("CONFIG_FILE", "config.json")


class BaseLoadTestUser(HttpUser):
    """Base user with common functionality"""
    
    abstract = True
    wait_time = between(1, 3)
    
    def on_start(self):
        """Initialize user with configuration"""
        self.host = HOST
        self.setup_config()
        logger.info(f"User started - Host: {self.host}")
    
    def setup_config(self):
        """Load configuration if available"""
        self.config = None
        if HAS_CONFIG and Config is not None:
            try:
                self.config = Config()  # No parameters for singleton
                if hasattr(self.config, 'test_host') and self.config.test_host:
                    self.host = self.config.test_host
                elif hasattr(self.config, 'base_url') and self.config.base_url:
                    self.host = self.config.base_url
            except Exception as e:
                logger.warning(f"Failed to load config: {e}")
                self.config = None
                logger.info(f"Loaded config from {CONFIG_FILE}")


class APITestUser(BaseLoadTestUser):
    """Standard API test user"""
    
    weight = 3
    
    @task(5)
    def test_health(self):
        """Health check endpoint"""
        self.client.get("/health", name="health_check")
    
    @task(3)
    def test_metrics(self):
        """Test metrics endpoints"""
        endpoints = ["/metrics", "/api/metrics"]
        endpoint = random.choice(endpoints)
        self.client.get(endpoint, name=f"metrics_{endpoint.replace('/', '_')}")
    
    @task(2)
    def test_echo(self):
        """Echo endpoint test"""
        messages = ["test", "load", "performance", "benchmark"]
        message = random.choice(messages)
        self.client.get(f"/echo/{message}", name="echo")
    
    @task(1)
    def test_cpu_intensive(self):
        """CPU intensive endpoint"""
        n = random.randint(1000, 5000)
        self.client.get(f"/cpu-intensive?n={n}", name="cpu_intensive")
    
    @task(1)
    def test_memory_intensive(self):
        """Memory intensive endpoint"""
        size = random.randint(1, 5)
        self.client.get(f"/memory-intensive?size_mb={size}", name="memory_intensive")
    
    @task(2)
    def test_redis_operations(self):
        """Redis operations"""
        key = f"key_{random.randint(1, 100)}"
        value = f"value_{random.randint(1, 1000)}"
        
        # Set value
        self.client.get(f"/redis/set/{key}/{value}", name="redis_set")
        
        # Get value
        self.client.get(f"/redis/get/{key}", name="redis_get")
    
    @task(3)
    def test_configured_endpoints(self):
        """Test endpoints from configuration"""
        if not self.config or not hasattr(self.config, 'endpoints'):
            return
        
        endpoints = self.config.endpoints
        if not endpoints:
            return
        
        endpoint = random.choice(endpoints)
        method = endpoint.get("method", "GET").upper()
        url = endpoint.get("url", "/")
        name = endpoint.get("id", url)
        
        if method == "GET":
            self.client.get(url, name=name)
        elif method == "POST":
            data = endpoint.get("data", {})
            self.client.post(url, json=data, name=name)
        elif method == "PUT":
            data = endpoint.get("data", {})
            self.client.put(url, json=data, name=name)
        elif method == "DELETE":
            self.client.delete(url, name=name)


class StressTestUser(BaseLoadTestUser):
    """User for stress testing with higher load"""
    
    weight = 2
    wait_time = between(0.5, 1.5)
    
    @task(10)
    def rapid_health_checks(self):
        """Rapid health checks"""
        for _ in range(3):
            self.client.get("/health", name="rapid_health")
            time.sleep(0.1)
    
    @task(5)
    def burst_requests(self):
        """Burst of mixed requests"""
        endpoints = ["/health", "/metrics", "/api/metrics", "/version"]
        for _ in range(5):
            endpoint = random.choice(endpoints)
            self.client.get(endpoint, name=f"burst_{endpoint.replace('/', '_')}")
            time.sleep(0.05)
    
    @task(2)
    def concurrent_redis(self):
        """Concurrent Redis operations"""
        operations = []
        for i in range(3):
            key = f"stress_key_{i}"
            value = f"stress_value_{random.randint(1, 1000)}"
            operations.append((key, value))
        
        # Set all values
        for key, value in operations:
            self.client.get(f"/redis/set/{key}/{value}", name="concurrent_redis_set")
        
        # Get all values
        for key, _ in operations:
            self.client.get(f"/redis/get/{key}", name="concurrent_redis_get")


class SpikeTestUser(BaseLoadTestUser):
    """User for spike testing"""
    
    weight = 1
    wait_time = between(0.1, 0.5)
    
    @task
    def spike_load(self):
        """Generate spike load patterns"""
        # Burst phase
        burst_size = random.randint(5, 10)
        for _ in range(burst_size):
            endpoint = random.choice(["/health", "/metrics", "/api/metrics"])
            self.client.get(endpoint, name=f"spike_{endpoint.replace('/', '_')}")
            time.sleep(0.01)
        
        # Rest phase
        time.sleep(random.uniform(2, 5))


class LoadTestUser(HttpUser):
    """Simple load test user"""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """Initialize user with configuration"""
        self.host = HOST  # Use environment variable
        if HAS_CONFIG and Config is not None:
            try:
                self.config = Config()  # No parameters for singleton
                if hasattr(self.config, 'test_host') and self.config.test_host:
                    self.host = self.config.test_host
                elif hasattr(self.config, 'base_url') and self.config.base_url:
                    self.host = self.config.base_url
            except Exception as e:
                logger.warning(f"Failed to load config: {e}, using default host")
                self.config = None
        else:
            self.config = None
    
    @task(3)
    def test_health(self):
        """Health check endpoint"""
        self.client.get("/health")
    
    @task(2)
    def test_metrics(self):
        """Metrics endpoint"""
        self.client.get("/metrics")
    
    @task(2)
    def test_api_metrics(self):
        """API metrics endpoint"""
        self.client.get("/api/metrics")
    
    @task(1)
    def test_echo(self):
        """Echo endpoint"""
        messages = ["test", "load", "performance", "benchmark"]
        message = random.choice(messages)
        self.client.get(f"/echo/{message}")
    
    @task(1)
    def test_cpu(self):
        """CPU intensive endpoint"""
        n = random.randint(1000, 5000)
        self.client.get(f"/cpu-intensive?n={n}")
    
    @task(1)
    def test_memory(self):
        """Memory intensive endpoint"""
        size = random.randint(1, 5)
        self.client.get(f"/memory-intensive?size_mb={size}")
    
    @task(1)
    def test_redis(self):
        """Redis operations"""
        key = f"key_{random.randint(1, 100)}"
        value = f"value_{random.randint(1, 1000)}"
        
        # Set
        self.client.get(f"/redis/set/{key}/{value}")
        
        # Get
        self.client.get(f"/redis/get/{key}")
    
    @task
    def test_configured_endpoints(self):
        """Test endpoints from configuration"""
        if self.config is None:
            return  # Skip if no config available
        
        # Get endpoints if available
        endpoints = getattr(self.config, 'endpoints', [])
        if endpoints:
            endpoint = random.choice(endpoints)
            method = endpoint.get("method", "GET").upper()
            url = endpoint.get("url", "/")
            
            if method == "GET":
                self.client.get(url, name=endpoint.get("id", url))
            elif method == "POST":
                data = endpoint.get("data", {})
                self.client.post(url, json=data, name=endpoint.get("id", url))
            elif method == "PUT":
                data = endpoint.get("data", {})
                self.client.put(url, json=data, name=endpoint.get("id", url))
            elif method == "DELETE":
                self.client.delete(url, name=endpoint.get("id", url))

# Metrics tracking
request_stats = {}
start_time = None


@events.request.add_listener
def on_request(request_type, name, response_time, response_length, response, context, exception, **kwargs):
    """Track request metrics"""
    if name not in request_stats:
        request_stats[name] = {
            'count': 0,
            'total_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'failures': 0,
            'success': 0
        }
    
    stats = request_stats[name]
    stats['count'] += 1
    stats['total_time'] += response_time
    stats['min_time'] = min(stats['min_time'], response_time)
    stats['max_time'] = max(stats['max_time'], response_time)
    
    if exception:
        stats['failures'] += 1
    else:
        stats['success'] += 1


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Test start handler"""
    global start_time
    start_time = time.time()
    logger.info("=" * 50)
    logger.info("Load Test Starting")
    logger.info(f"Target Host: {environment.host}")
    logger.info(f"Config File: {CONFIG_FILE}")
    logger.info("=" * 50)


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Test stop handler - print summary"""
    global start_time
    duration = time.time() - start_time if start_time else 0
    
    print("\n" + "=" * 60)
    print("LOAD TEST SUMMARY")
    print("=" * 60)
    print(f"Duration: {duration:.2f} seconds")
    print(f"Target Host: {environment.host}")
    
    # Overall statistics
    total_requests = sum(s['count'] for s in request_stats.values())
    total_failures = sum(s['failures'] for s in request_stats.values())
    
    if total_requests > 0:
        success_rate = ((total_requests - total_failures) / total_requests) * 100
        print(f"\nOverall Statistics:")
        print(f"  Total Requests: {total_requests:,}")
        print(f"  Successful: {total_requests - total_failures:,}")
        print(f"  Failed: {total_failures:,}")
        print(f"  Success Rate: {success_rate:.2f}%")
        print(f"  Requests/sec: {total_requests / duration:.2f}")
    
    # Per-endpoint statistics
    if request_stats:
        print("\nEndpoint Statistics:")
        print("-" * 60)
        print(f"{'Endpoint':<30} {'Reqs':<8} {'Avg(ms)':<10} {'Min(ms)':<10} {'Max(ms)':<10} {'Fail%':<8}")
        print("-" * 60)
        
        for name, stats in sorted(request_stats.items()):
            if stats['count'] > 0:
                avg_time = stats['total_time'] / stats['count']
                fail_rate = (stats['failures'] / stats['count']) * 100
                print(f"{name:<30} {stats['count']:<8} {avg_time:<10.2f} {stats['min_time']:<10.2f} {stats['max_time']:<10.2f} {fail_rate:<8.2f}")
    
    print("=" * 60)
    print("Test completed!")
    print("=" * 60)


# Main execution
if __name__ == "__main__":
    print("LoadTester - Simplified Locust Framework")
    print(f"Config: {CONFIG_FILE}")
    print(f"Host: {HOST}")
    print("Use 'locust -f locustfile.py' to run")