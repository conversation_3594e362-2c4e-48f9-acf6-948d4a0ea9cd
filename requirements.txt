# Enhanced Locust Load Testing Framework Requirements
# ================================================

# Core Dependencies
locust>=2.17.0
requests>=2.31.0
httpx>=0.25.0

# Configuration and Data Handling
pydantic>=2.5.0
pyyaml>=6.0.1
python-dotenv>=1.0.0

# JWT Token Handling
pyjwt>=2.8.0
cryptography>=41.0.0

# System Monitoring
psutil>=5.9.0

# Data Analysis and Reporting
pandas>=2.1.0
numpy>=1.24.0

# Visualization (for enhanced HTML reports)
plotly>=5.17.0
jinja2>=3.1.0

# Time Series and Metrics
influxdb-client>=1.38.0

# Async Support
aiohttp>=3.9.0
asyncio-mqtt>=0.13.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Optional: Database connectivity (if needed for test data)
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0
# pymongo>=4.6.0

# Optional: Message queue integration (if needed)
# pika>=1.3.0
# redis>=5.0.0

# Optional: Cloud integrations (if needed)
# boto3>=1.34.0
# azure-storage-blob>=12.19.0
# google-cloud-storage>=2.10.0
