from fastapi import FastAP<PERSON>, HTTPException, Depends, status, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import json
from datetime import datetime
import redis
import random
import string
import time
import psutil
import threading

app = FastAPI(title="Load Test API",
              description="API for load testing purposes",
              version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Redis setup
redis_client = redis.Redis(
    host=os.getenv('REDIS_HOST', 'localhost'),
    port=int(os.getenv('REDIS_PORT', 6379)),
    db=0,
    decode_responses=True
)

# Models
class Item(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    tax: Optional[float] = None

class ResponseModel(BaseModel):
    status: str
    message: str
    data: Optional[dict] = None

# Health check endpoint
@app.get("/health", response_model=ResponseModel)
async def health_check():
    return {
        "status": "success",
        "message": "API is running",
        "data": {
            "timestamp": datetime.utcnow().isoformat(),
            "status": "healthy"
        }
    }

# Version endpoint
@app.get("/version")
async def get_version():
    """Get API version information"""
    return {
        "version": "1.0.0",
        "name": "Load Test API",
        "description": "API for load testing purposes",
        "timestamp": datetime.utcnow().isoformat()
    }

# Simple echo endpoint
@app.get("/echo/{message}", response_model=ResponseModel)
async def echo_message(message: str):
    return {
        "status": "success",
        "message": f"Echo: {message}",
        "data": {
            "original_message": message,
            "timestamp": datetime.utcnow().isoformat()
        }
    }

# CPU intensive endpoint
@app.get("/cpu-intensive", response_model=ResponseModel)
async def cpu_intensive(n: int = 10000):
    start_time = time.time()
    # Perform some CPU-intensive operations
    result = 0
    for i in range(n):
        result += i * i
    
    return {
        "status": "success",
        "message": f"Processed {n} iterations",
        "data": {
            "result": result,
            "processing_time": time.time() - start_time
        }
    }

# Memory intensive endpoint
@app.get("/memory-intensive", response_model=ResponseModel)
async def memory_intensive(size_mb: int = 10):
    start_time = time.time()
    # Allocate approximately size_mb MB of data
    data = 'x' * (size_mb * 1024 * 1024)
    
    # Do something with the data to prevent it from being optimized away
    result = len(data)
    
    return {
        "status": "success",
        "message": f"Processed {size_mb}MB of data",
        "data": {
            "data_size_bytes": result,
            "processing_time": time.time() - start_time
        }
    }

# Redis operations
@app.get("/redis/set/{key}/{value}", response_model=ResponseModel)
async def redis_set(key: str, value: str):
    try:
        redis_client.set(key, value)
        return {
            "status": "success",
            "message": f"Set {key} in Redis",
            "data": {
                "key": key,
                "value": value
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Redis error: {str(e)}"
        )

@app.get("/redis/get/{key}", response_model=ResponseModel)
async def redis_get(key: str):
    try:
        value = redis_client.get(key)
        if value is None:
            raise HTTPException(
                status_code=404,
                detail=f"Key {key} not found"
            )
            
        return {
            "status": "success",
            "message": f"Retrieved {key} from Redis",
            "data": {
                "key": key,
                "value": value
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Redis error: {str(e)}"
        )

# File upload endpoint
@app.post("/upload", response_model=ResponseModel)
async def upload_file(file: UploadFile = File(...)):
    try:
        # Generate a random filename
        filename = f"file_{int(time.time())}_{''.join(random.choices(string.ascii_letters + string.digits, k=8))}.bin"
        filepath = os.path.join("uploads", filename)
        
        # Ensure uploads directory exists
        os.makedirs("uploads", exist_ok=True)
        
        # Write the file
        contents = await file.read()
        with open(filepath, "wb") as f:
            f.write(contents)
            
        return {
            "status": "success",
            "message": "File uploaded successfully",
            "data": {
                "filename": filename,
                "size_bytes": len(contents),
                "saved_path": filepath
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"File upload failed: {str(e)}"
        )

# Error endpoint for testing error rates
@app.get("/error/{rate}", response_model=ResponseModel)
async def error_endpoint(rate: float = 0.5):
    if not 0 <= rate <= 1:
        raise HTTPException(
            status_code=400,
            detail="Rate must be between 0 and 1"
        )
    
    if random.random() < rate:
        raise HTTPException(
            status_code=500,
            detail="Random error occurred (this is expected for testing)"
        )
    
    return {
        "status": "success",
        "message": "Request succeeded (no error)",
        "data": {
            "error_rate": rate,
            "timestamp": datetime.utcnow().isoformat()
        }
    }

# Metrics tracking
request_count = 0
error_count = 0
start_time = time.time()
request_lock = threading.Lock()

@app.middleware("http")
async def track_metrics(request, call_next):
    global request_count, error_count
    with request_lock:
        request_count += 1
    
    start = time.time()
    response = await call_next(request)
    
    if response.status_code >= 400:
        with request_lock:
            error_count += 1
    
    return response

# Metrics endpoint (Prometheus format)
@app.get("/metrics", response_class=PlainTextResponse)
async def metrics():
    uptime = time.time() - start_time
    cpu_percent = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    
    # Prometheus format metrics
    metrics_text = f"""# HELP app_info Application information
# TYPE app_info gauge
app_info{{version="1.0.0"}} 1

# HELP http_requests_total Total HTTP requests
# TYPE http_requests_total counter
http_requests_total {request_count}

# HELP http_errors_total Total HTTP errors
# TYPE http_errors_total counter
http_errors_total {error_count}

# HELP uptime_seconds Application uptime in seconds
# TYPE uptime_seconds gauge
uptime_seconds {uptime:.2f}

# HELP cpu_usage_percent CPU usage percentage
# TYPE cpu_usage_percent gauge
cpu_usage_percent {cpu_percent}

# HELP memory_usage_bytes Memory usage in bytes
# TYPE memory_usage_bytes gauge
memory_usage_bytes {memory.used}

# HELP memory_available_bytes Available memory in bytes
# TYPE memory_available_bytes gauge
memory_available_bytes {memory.available}

# HELP memory_percent Memory usage percentage
# TYPE memory_percent gauge
memory_percent {memory.percent}
"""
    return metrics_text

# JSON metrics endpoint
@app.get("/api/metrics")
async def api_metrics() -> Dict[str, Any]:
    uptime = time.time() - start_time
    cpu_percent = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    
    try:
        redis_info = redis_client.info()
        redis_connected = True
    except:
        redis_info = {}
        redis_connected = False
    
    return {
        "application": {
            "version": "1.0.0",
            "uptime_seconds": round(uptime, 2),
            "requests_total": request_count,
            "errors_total": error_count,
            "error_rate": round(error_count / max(request_count, 1) * 100, 2)
        },
        "system": {
            "cpu_percent": cpu_percent,
            "memory_used_bytes": memory.used,
            "memory_available_bytes": memory.available,
            "memory_percent": memory.percent
        },
        "redis": {
            "connected": redis_connected,
            "used_memory": redis_info.get("used_memory", 0) if redis_connected else 0,
            "connected_clients": redis_info.get("connected_clients", 0) if redis_connected else 0
        },
        "timestamp": datetime.utcnow().isoformat()
    }
