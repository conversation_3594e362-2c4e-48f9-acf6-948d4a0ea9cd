worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 100;
    types_hash_max_size 2048;
    
    # Buffer settings
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 8k;
    output_buffers 32 32k;
    postpone_output 1460;
    
    # Timeouts
    client_header_timeout 60s;
    client_body_timeout 60s;
    send_timeout 60s;
    
    # Gzip
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/rss+xml application/atom+xml image/svg+xml text/x-js text/x-cross-domain-policy application/x-font-ttf application/x-font-opentype application/vnd.ms-fontobject image/x-icon;
    
    # Upstream configuration for load balancing
    upstream fastapi_backend {
        least_conn;
        
        # Multiple FastAPI instances (if running multiple containers)
        server loadtest-api:8001 max_fails=3 fail_timeout=30s;
        
        # Connection pool settings
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=general:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=50r/s;
    limit_conn_zone $binary_remote_addr zone=addr:10m;
    
    server {
        listen 80;
        server_name localhost;
        
        # Connection limits
        limit_conn addr 100;
        
        # Proxy settings
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Proxy buffering
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        proxy_temp_file_write_size 8k;
        
        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://fastapi_backend;
            proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
        }
        
        # Auth endpoints (moderate rate limiting)
        location ~ ^/auth/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://fastapi_backend;
            proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
        }
        
        # API endpoints (standard rate limiting)
        location /api/ {
            limit_req zone=general burst=50 nodelay;
            proxy_pass http://fastapi_backend;
            proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
        }
        
        # Metrics endpoints (no rate limiting for monitoring)
        location /metrics {
            proxy_pass http://fastapi_backend;
        }
        
        # Default location
        location / {
            limit_req zone=general burst=50 nodelay;
            proxy_pass http://fastapi_backend;
            proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
        }
        
        # Error pages
        error_page 502 503 504 /50x.html;
        location = /50x.html {
            return 503 '{"error": "Service temporarily unavailable"}';
            add_header Content-Type application/json;
        }
    }
}