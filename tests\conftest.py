"""
Pytest configuration and fixtures for the test suite.
"""
import pytest
import asyncio
from pathlib import Path
import sys

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_config():
    """Provide a test configuration."""
    return {
        "target_url": "http://localhost:8001",
        "users": 10,
        "spawn_rate": 2,
        "run_time": "10s",
        "test_type": "smoke"
    }


@pytest.fixture
def mock_client():
    """Provide a mock HTTP client for testing."""
    import httpx
    return httpx.AsyncClient()