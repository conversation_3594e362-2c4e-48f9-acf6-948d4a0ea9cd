events {
    worker_connections 1024;
}

http {
    # Rate limiting - must be in http context
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/s;
    
    upstream loadtester {
        server loadtester:8001;
        # Add more servers here for load balancing
        # server loadtester2:8001;
        # server loadtester3:8001;
    }

    server {
        listen 80;
        
        # Apply rate limiting
        limit_req zone=api burst=50 nodelay;
        
        # Proxy settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        location / {
            proxy_pass http://loadtester;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Connection pooling
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        location /health {
            proxy_pass http://loadtester/health;
            access_log off;
        }
    }
}