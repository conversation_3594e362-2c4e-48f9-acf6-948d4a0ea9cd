#!/usr/bin/env python3
"""
Setup script for Enhanced Locust Load Testing Framework
======================================================
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "docs" / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
with open('requirements.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            # Remove comments from the line
            requirement = line.split('#')[0].strip()
            if requirement:
                requirements.append(requirement)

setup(
    name="enhanced-locust-framework",
    version="1.0.0",
    author="Load Testing Team",
    author_email="<EMAIL>",
    description="Enhanced Locust Load Testing Framework with advanced features",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/enhanced-locust-framework",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/enhanced-locust-framework/issues",
        "Documentation": "https://github.com/your-org/enhanced-locust-framework/docs",
        "Source Code": "https://github.com/your-org/enhanced-locust-framework",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "Topic :: Software Development :: Testing",
        "Topic :: Software Development :: Testing :: Traffic Generation",
        "Topic :: System :: Monitoring",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.7.0",
        ],
        "docs": [
            "mkdocs>=1.5.0",
            "mkdocs-material>=9.4.0",
        ],
        "monitoring": [
            "influxdb-client>=1.38.0",
            "prometheus-client>=0.19.0",
        ],
        "cloud": [
            "boto3>=1.34.0",
            "azure-storage-blob>=12.19.0",
            "google-cloud-storage>=2.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "enhanced-locust=locust_framework.cli:main",
            "locust-config=locust_framework.config_cli:main",
            "locust-monitor=locust_framework.monitor_cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "locust_framework": [
            "templates/*.html",
            "static/*.css",
            "static/*.js",
        ],
    },
    zip_safe=False,
    keywords=[
        "load testing",
        "performance testing",
        "locust",
        "api testing",
        "stress testing",
        "monitoring",
        "reporting",
        "jwt",
        "authentication",
    ],
)
