# Load Testing Best Practices

This guide outlines best practices for effective load testing using the enhanced Locust framework.

## 🎯 Test Planning and Strategy

### 1. Define Clear Objectives

Before starting any load test, clearly define:

- **Performance Goals**: Response time targets, throughput requirements
- **User Load Patterns**: Expected concurrent users, peak traffic scenarios
- **Success Criteria**: What constitutes a passing test
- **Failure Conditions**: When to stop a test (error rates, response times)

```bash
# Example: E-commerce site goals
# - Handle 1000 concurrent users during peak hours
# - 95% of requests complete within 2 seconds
# - Error rate below 1%
# - System remains stable for 4+ hours
```

### 2. Test Pyramid Approach

Follow a structured testing approach:

1. **Smoke Tests** (1-5 users): Basic functionality verification
2. **Load Tests** (expected load): Normal operating conditions
3. **Stress Tests** (beyond capacity): Find breaking points
4. **Spike Tests** (sudden increases): Handle traffic bursts
5. **Endurance Tests** (long duration): Stability over time

```bash
# Recommended test sequence
python scripts/run_load_tests.py --test-type smoke --context myapp --users 1
python scripts/run_load_tests.py --test-type load --context myapp --users 100
python scripts/run_load_tests.py --test-type stress --context myapp --users 500
python scripts/run_load_tests.py --test-type spike --context myapp --users 200 --spawn-rate 50
python scripts/run_load_tests.py --test-type endurance --context myapp --users 50 --duration 4h
```

## 🔧 Configuration Best Practices

### 3. Environment-Specific Configurations

Always use separate configurations for different environments:

```json
// configs/environments.json
{
  "development": {
    "base_url": "http://localhost:8000",
    "num_users": 5,
    "verify_ssl": false,
    "connection_timeout": 10.0
  },
  "staging": {
    "base_url": "https://staging-api.example.com",
    "num_users": 50,
    "verify_ssl": true,
    "connection_timeout": 30.0
  },
  "production": {
    "base_url": "https://api.example.com",
    "num_users": 100,
    "verify_ssl": true,
    "connection_timeout": 30.0,
    "read_timeout": 60.0
  }
}
```

### 4. JWT Token Management

- **Validate tokens before testing**: Use `--validate-jwt` flag
- **Monitor token expiration**: Set up alerts for tokens expiring soon
- **Rotate tokens regularly**: Update tokens in CI/CD pipelines
- **Use service accounts**: Avoid personal tokens for automated testing

```bash
# Validate token before important tests
python scripts/manage_config.py --validate-jwt

# Update token when needed
python scripts/manage_config.py --update-jwt "new_token_here"
```

### 5. Realistic User Behavior

Model realistic user patterns:

```python
class RealisticUser(AuthenticatedUser):
    # Realistic think time between actions
    wait_time = between(2, 8)
    
    def on_start(self):
        super().on_start()
        # Simulate user login/session creation
        self.create_user_session()
    
    @task(10)  # Most common action
    def browse_content(self):
        """Users spend most time browsing"""
        pass
    
    @task(3)   # Less frequent
    def search(self):
        """Occasional searches"""
        pass
    
    @task(1)   # Rare action
    def purchase(self):
        """Infrequent purchases"""
        pass
```

## 📊 Test Execution Best Practices

### 6. Gradual Load Increase

Always ramp up load gradually:

```bash
# Good: Gradual ramp-up
python scripts/run_load_tests.py \
  --test-type load \
  --context myapp \
  --users 100 \
  --spawn-rate 5 \
  --duration 15m

# Avoid: Instant load spike (unless testing spike scenarios)
# --spawn-rate 100  # This creates instant load
```

### 7. Test Duration Guidelines

- **Smoke Tests**: 30 seconds - 2 minutes
- **Load Tests**: 10-30 minutes
- **Stress Tests**: 10-20 minutes
- **Spike Tests**: 5-15 minutes
- **Endurance Tests**: 2-8 hours

### 8. Resource Monitoring

Always monitor system resources during tests:

```bash
# Enable monitoring for all important tests
python scripts/run_load_tests.py \
  --test-type load \
  --context myapp \
  --users 100 \
  --monitor \
  --export-metrics
```

**Key Metrics to Monitor:**
- CPU usage (target: <80%)
- Memory usage (target: <85%)
- Network I/O
- Disk I/O
- Database connections
- Application-specific metrics

## 🚨 Error Handling and Alerting

### 9. Alert Configuration

Set up meaningful alerts:

```python
from locust_framework.real_time_monitor import RealTimeMonitor

monitor = RealTimeMonitor()

# Critical alerts
monitor.alert_manager.add_alert_rule(
    name="critical_error_rate",
    condition=lambda s: s.failures_per_second > s.requests_per_second * 0.05,
    message="Error rate exceeds 5%",
    severity="critical"
)

# Performance alerts
monitor.alert_manager.add_alert_rule(
    name="high_response_time",
    condition=lambda s: s.avg_response_time > 3000,
    message="Average response time exceeds 3 seconds",
    severity="warning"
)

# Resource alerts
monitor.alert_manager.add_alert_rule(
    name="high_memory_usage",
    condition=lambda s: s.memory_percent > 90,
    message="Memory usage exceeds 90%",
    severity="warning"
)
```

### 10. Error Analysis

Implement proper error handling:

```python
class RobustUser(AuthenticatedUser):
    def __init__(self, environment):
        super().__init__(environment)
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
    
    @task
    def api_call_with_retry(self):
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with self.authenticated_request("GET", "/api/data") as response:
                    if response.status_code == 200:
                        self.consecutive_errors = 0  # Reset error counter
                        response.success()
                        return
                    elif response.status_code >= 500:
                        # Server error - retry
                        if attempt < max_retries - 1:
                            time.sleep(2 ** attempt)  # Exponential backoff
                            continue
                    
                    # Client error or final retry
                    self.consecutive_errors += 1
                    response.failure(f"API call failed: {response.status_code}")
                    
            except Exception as e:
                self.consecutive_errors += 1
                if attempt == max_retries - 1:
                    # Log final failure
                    logger.error(f"API call failed after {max_retries} retries: {e}")
        
        # Stop user if too many consecutive errors
        if self.consecutive_errors >= self.max_consecutive_errors:
            logger.error(f"User stopping due to {self.consecutive_errors} consecutive errors")
            self.environment.runner.quit()
```

## 📈 Performance Optimization

### 11. Connection Management

Optimize HTTP connections:

```python
# Good: Reuse connections
class EfficientUser(AuthenticatedUser):
    connection_timeout = 30.0
    read_timeout = 60.0
    
    def on_start(self):
        super().on_start()
        # Configure connection pooling
        self.client.pool_connections = 10
        self.client.pool_maxsize = 10
```

### 12. Data Management

Handle test data efficiently:

```python
class DataEfficientUser(AuthenticatedUser):
    def on_start(self):
        super().on_start()
        # Load test data once per user
        self.test_data = self.load_test_data()
    
    def load_test_data(self):
        """Load test data from file or generate"""
        # Load from CSV, JSON, or generate programmatically
        return [
            {"user_id": i, "email": f"user{i}@example.com"}
            for i in range(1000)
        ]
    
    @task
    def use_test_data(self):
        # Use pre-loaded data instead of generating each time
        user_data = random.choice(self.test_data)
        # Use user_data in request
```

## 🔍 Analysis and Reporting

### 13. Meaningful Metrics

Focus on business-relevant metrics:

```python
# Custom metrics for business impact
report_manager.add_custom_metric("successful_orders", order_count)
report_manager.add_custom_metric("revenue_per_minute", revenue_rate)
report_manager.add_custom_metric("user_satisfaction_score", satisfaction)
```

### 14. Baseline Comparisons

Always compare against baselines:

```bash
# Establish baseline
python scripts/run_load_tests.py --test-type baseline --context myapp --users 1

# Compare load test results against baseline
python scripts/run_load_tests.py --test-type load --context myapp --users 100
```

### 15. Trend Analysis

Track performance over time:

```bash
# Regular performance regression tests
# Run daily/weekly to catch performance degradation early
python scripts/run_load_tests.py \
  --test-type load \
  --context myapp \
  --users 50 \
  --duration 10m \
  --export-metrics
```

## 🛡️ Security and Safety

### 16. Test Environment Safety

- **Never test production without approval**
- **Use dedicated test environments**
- **Implement circuit breakers**
- **Monitor downstream systems**

```python
# Circuit breaker pattern
class SafeUser(AuthenticatedUser):
    def __init__(self, environment):
        super().__init__(environment)
        self.circuit_breaker_threshold = 10
        self.consecutive_failures = 0
        self.circuit_open = False
    
    @task
    def safe_api_call(self):
        if self.circuit_open:
            # Circuit is open, don't make requests
            return
        
        with self.authenticated_request("GET", "/api/data") as response:
            if response.status_code >= 500:
                self.consecutive_failures += 1
                if self.consecutive_failures >= self.circuit_breaker_threshold:
                    self.circuit_open = True
                    logger.warning("Circuit breaker opened due to consecutive failures")
                response.failure(f"Server error: {response.status_code}")
            else:
                self.consecutive_failures = 0
                response.success()
```

### 17. Data Privacy

- **Use synthetic test data**
- **Avoid real user data**
- **Implement data masking**
- **Follow GDPR/privacy regulations**

## 🔄 CI/CD Integration

### 18. Automated Testing

Integrate load tests into CI/CD:

```yaml
# Example GitHub Actions workflow
name: Load Testing
on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:

jobs:
  load-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -e .
      - name: Run smoke test
        run: |
          python scripts/run_load_tests.py \
            --test-type smoke \
            --context myapp \
            --users 1
      - name: Run load test
        run: |
          python scripts/run_load_tests.py \
            --test-type load \
            --context myapp \
            --users 50 \
            --duration 10m
```

### 19. Performance Gates

Implement performance gates:

```bash
# Fail build if performance degrades
python scripts/run_load_tests.py \
  --test-type load \
  --context myapp \
  --users 100 \
  --duration 10m \
  --performance-gate \
  --max-avg-response-time 2000 \
  --min-success-rate 99.0
```

## 📚 Documentation and Knowledge Sharing

### 20. Test Documentation

Document your tests:

```python
class DocumentedUser(AuthenticatedUser):
    """
    E-commerce user simulation for load testing.
    
    Simulates typical user journey:
    1. Browse products (70% of time)
    2. Add to cart (20% of time) 
    3. Checkout (10% of time)
    
    Expected load: 1000 concurrent users during peak hours
    Target response time: <2 seconds for 95% of requests
    """
    
    @task(7)
    def browse_products(self):
        """Browse product catalog - most common user action"""
        pass
    
    @task(2)
    def add_to_cart(self):
        """Add items to shopping cart"""
        pass
    
    @task(1)
    def checkout(self):
        """Complete purchase transaction"""
        pass
```

### 21. Results Sharing

Share results effectively:

- **Executive Summary**: High-level metrics and business impact
- **Technical Details**: Response times, error rates, resource usage
- **Recommendations**: Performance improvements and capacity planning
- **Trend Analysis**: Comparison with previous tests

By following these best practices, you'll create more effective, reliable, and maintainable load tests that provide valuable insights into your system's performance characteristics.
