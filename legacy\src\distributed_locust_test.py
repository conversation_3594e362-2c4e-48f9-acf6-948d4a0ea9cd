#!/usr/bin/env python3
"""
Distributed load testing using Locust for 2000+ users
Run with: locust -f scripts/distributed_locust_test.py --host=https://mia-ta.lacaisse.com
"""

from locust import HttpUser, task, between, events
import json
import random
import time

class AdvancedAPIUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Initialize user session"""
        with open("config.global.json", "r") as f:
            self.config = json.load(f)
        
        self.jwt_token = self.config["jwt_token"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        self.session_data = {}
        
        # Create session for this user
        self.create_session()
    
    def create_session(self):
        """Create a new session for the user"""
        endpoints = [
            "/andoc/session/new",
            "/talperftraitement/session/new"
        ]
        
        endpoint = random.choice(endpoints)
        
        with self.client.post(endpoint, headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.session_data["session_id"] = data.get("session_id")
                response.success()
            else:
                response.failure(f"Session creation failed: {response.status_code}")
    
    @task(3)
    def send_message(self):
        """Send a message to the API"""
        if not self.session_data.get("session_id"):
            self.create_session()
            return
        
        messages = [
            "Bonjour, comment allez-vous?",
            "Pouvez-vous m'aider avec ma demande?",
            "Quelles sont les options disponibles?",
            "Merci pour votre aide",
            "J'ai une question sur les congés"
        ]
        
        endpoints = [
            "/andoc/chat/send",
            "/talperftraitement/rag/message"
        ]
        
        endpoint = random.choice(endpoints)
        message = random.choice(messages)
        
        payload = {
            "message": message,
            "session_id": self.session_data["session_id"]
        }
        
        with self.client.post(endpoint, json=payload, headers=self.headers, 
                            catch_response=True, name=f"{endpoint}_message") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Message failed: {response.status_code}")
    
    @task(1)
    def health_check(self):
        """Periodic health check"""
        with self.client.get("/health", headers=self.headers, 
                           catch_response=True, name="health_check") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")

# Event listeners for detailed logging
@events.request.add_listener
def log_request(request_type, name, response_time, response_length, response, exception, **kwargs):
    """Log all requests with detailed information"""
    if exception:
        print(f"❌ {name}: Exception - {exception}")
    elif response and response.status_code >= 400:
        print(f"⚠️  {name}: HTTP {response.status_code} - {response_time}ms")
    elif response_time > 5000:  # Log slow requests
        print(f"🐌 {name}: Slow response - {response_time}ms")

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    print("🚀 Starting distributed load test for 2000 users...")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    print("🏁 Load test completed!")
    
    # Generate summary report
    stats = environment.stats
    print(f"\n📊 FINAL RESULTS:")
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Failed requests: {stats.total.num_failures}")
    print(f"Success rate: {((stats.total.num_requests - stats.total.num_failures) / stats.total.num_requests * 100):.1f}%")
    print(f"Average response time: {stats.total.avg_response_time:.0f}ms")
    print(f"95th percentile: {stats.total.get_response_time_percentile(0.95):.0f}ms")