.PHONY: help install test clean run-locust docker-up docker-down

help:
	@echo "Available commands:"
	@echo "  make install        - Install dependencies"
	@echo "  make test          - Run tests"
	@echo "  make clean         - Clean up generated files"
	@echo "  make run           - Run load test with Locust UI"
	@echo "  make run-headless  - Run load test without UI"
	@echo "  make analyze-full  - Run complete load test with backend monitoring and reports"
	@echo "  make excel-report  - Generate Excel report from existing test data"
	@echo "  make docker-up     - Start Docker services"
	@echo "  make docker-down   - Stop Docker services"

install:
	pip install -r requirements.txt
	pip install -e .

test:
	pytest tests/ -v

clean:
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	rm -rf reports/*.json reports/*.html reports/*.md reports/*.xlsx reports/run_* 
	rm -rf logs/*.log logs/run_*
	rm -rf .pytest_cache
	rm -rf *.egg-info
	rm -rf dist build
	rm -rf reports/*.csv reports/*.html

# Locust commands
run:
	python -m loadtester.cli --host http://localhost:8001

run-headless:
	python -m loadtester.cli --host http://localhost:8001 --headless -u 50 -r 5 -t 1m

run-stress:
	python -m loadtester.cli --host http://localhost:8001 --headless -u 200 -r 10 -t 5m

run-scenarios:
	python scenarios.py

# Docker commands
docker-up:
	docker-compose up -d
	@echo "Services started:"
	@echo "  - API: http://localhost:8001"
	@echo "  - PostgreSQL: localhost:5432"
	@echo "  - Redis: localhost:6379"

docker-up-all:
	docker-compose --profile monitoring --profile with-nginx up -d
	@echo "All services started:"
	@echo "  - API: http://localhost:8001"
	@echo "  - Nginx: http://localhost"
	@echo "  - Prometheus: http://localhost:9090"
	@echo "  - Grafana: http://localhost:3000"

docker-down:
	docker-compose --profile monitoring --profile with-nginx down --volumes

docker-logs:
	docker-compose logs -f

# Quick test
quick-test:
	@echo "Starting quick smoke test..."
	python -m loadtester.cli --headless -u 5 -r 1 -t 30s

# Generate analysis report (traditional)
analyze:
	@echo "Running load test and generating analysis report..."
	@mkdir -p reports
	python -m loadtester.cli --headless -u 10 -r 2 -t 30s --csv reports/analysis --html reports/analysis.html
	@echo "Generating scaling recommendations..."
	python -m loadtester.reports.analyzer reports/analysis_stats.csv reports/analysis_stats_history.csv 2000 reports/
	@echo "Creating visual dashboard..."
	python -m loadtester.reports.visualizer reports/scaling_report.json reports/
	@echo "Analysis complete! Open reports/scaling_dashboard.html to view the report."

# Generate analysis with backend monitoring
analyze-full:
	@echo "Running integrated load test with backend monitoring..."
	@mkdir -p reports
	@echo "Starting load test..."
	@python -m loadtester.monitor.runner -u 10 -r 2 -t 30s --host http://localhost:8001 --output reports
	@echo "Running analysis pipeline..."
	@python scripts/run_analysis.py
	@echo "Full analysis complete! Check reports/run_* directories for all outputs."

# Monitor backend only
monitor-backend:
	@echo "Starting backend monitoring..."
	@mkdir -p reports
	python -c "from loadtester.monitor import BackendMonitor; import time; m = BackendMonitor('reports'); m.start_monitoring(); time.sleep(30); m.stop_monitoring(); print('Metrics saved to reports/backend_metrics.json')"

# Quick integrated test
quick-integrated:
	@echo "Running quick integrated test..."
	@mkdir -p reports
	python -m loadtester.monitor.runner -u 5 -r 1 -t 15s --output reports

# Generate Excel report from existing data
excel-report:
	@echo "Generating Excel report from existing data..."
	@python -c "import subprocess, os, glob; \
	latest_dir = max([d for d in glob.glob('reports/run_*')], key=os.path.getctime) if glob.glob('reports/run_*') else 'reports'; \
	print(f'Using report directory: {latest_dir}'); \
	subprocess.run(['python', '-m', 'loadtester.reports.excel_generator', '--reports-dir', latest_dir]); \
	print(f'Excel report generated! Check {latest_dir}/ directory.')"