#!/usr/bin/env python3
"""
Latency Distribution Analysis - Track response time percentiles (P50, P95, P99)
"""
import asyncio
import httpx
import json
import statistics
import numpy as np
from datetime import datetime

class LatencyAnalyzer:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.config['jwt_token']}"}

    async def run_latency_analysis(self, sample_size: int = 1000):
        """Analyze latency distribution across endpoints"""
        print(f"📊 Starting latency analysis with {sample_size} samples...")
        
        endpoints = [
            "/health",
            "/andoc/session/new",
            "/talperftraitement/session/new"
        ]
        
        results = {}
        for endpoint in endpoints:
            print(f"\n📈 Analyzing {endpoint}...")
            latencies = await self._collect_latency_samples(endpoint, sample_size)
            analysis = self._analyze_latencies(latencies)
            results[endpoint] = analysis
        
        await self._save_latency_analysis(results)

    async def _collect_latency_samples(self, endpoint: str, sample_size: int):
        """Collect latency samples for an endpoint"""
        latencies = []
        
        async def measure_request():
            async with httpx.AsyncClient(timeout=30.0) as client:
                try:
                    start_time = time.time()
                    response = await client.get(f"{self.base_url}{endpoint}", headers=self.headers)
                    latency = (time.time() - start_time) * 1000  # Convert to ms
                    return latency if response.status_code == 200 else None
                except:
                    return None
        
        # Collect samples with some concurrency
        batch_size = 50
        for i in range(0, sample_size, batch_size):
            batch_tasks = [measure_request() for _ in range(min(batch_size, sample_size - i))]
            batch_results = await asyncio.gather(*batch_tasks)
            latencies.extend([l for l in batch_results if l is not None])
            
            if i % 200 == 0:
                print(f"   Collected {len(latencies)} samples...")
        
        return latencies

    def _analyze_latencies(self, latencies):
        """Calculate percentile statistics"""
        if not latencies:
            return {"error": "No valid samples collected"}
        
        return {
            "sample_count": len(latencies),
            "mean": statistics.mean(latencies),
            "median": statistics.median(latencies),
            "p50": np.percentile(latencies, 50),
            "p90": np.percentile(latencies, 90),
            "p95": np.percentile(latencies, 95),
            "p99": np.percentile(latencies, 99),
            "min": min(latencies),
            "max": max(latencies),
            "std_dev": statistics.stdev(latencies) if len(latencies) > 1 else 0
        }

if __name__ == "__main__":
    asyncio.run(LatencyAnalyzer().run_latency_analysis())