#!/usr/bin/env python3
"""
Centralized Logging Configuration
Provides datetime-based log organization and structured error logging
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class LoadTestLogger:
    """Centralized logger for the load testing framework"""
    
    _instance: Optional['LoadTestLogger'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            # Set default values but don't setup logging yet
            self.base_logs_dir = Path("logs")
            self.run_logs_dir = self.base_logs_dir
            self.log_files = {}
            self.main_logger = None
            self.error_logger = None
            self.locust_logger = None
            self.backend_logger = None
            self.reports_logger = None
            LoadTestLogger._initialized = True
    
    def setup_logging(self, base_logs_dir: str = "logs", run_id: Optional[str] = None):
        """Setup datetime-based logging structure"""
        
        # Skip if already setup for this run_id
        if run_id and self.run_logs_dir.name == run_id:
            return
            
        # Create base logs directory
        self.base_logs_dir = Path(base_logs_dir)
        self.base_logs_dir.mkdir(exist_ok=True)
        
        # Create datetime-based subdirectory for this run
        if run_id is None:
            self.timestamp = datetime.now()
            run_id = f"run_{self.timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        self.run_logs_dir = self.base_logs_dir / run_id
        self.run_logs_dir.mkdir(exist_ok=True)
        
        # Setup different log files
        self.log_files = {
            'main': self.run_logs_dir / 'loadtest.log',
            'errors': self.run_logs_dir / 'errors.log',
            'locust': self.run_logs_dir / 'locust.log',
            'backend': self.run_logs_dir / 'backend_monitoring.log',
            'reports': self.run_logs_dir / 'report_generation.log'
        }
        
        # Configure loggers
        self._setup_main_logger()
        self._setup_error_logger()
        self._setup_component_loggers()
        
        # Only print once for new directories
        if not hasattr(self, '_logs_dir_printed'):
            print(f"Logs directory: {self.run_logs_dir}")
            self._logs_dir_printed = True
        
    def _setup_main_logger(self):
        """Setup main application logger"""
        self.main_logger = logging.getLogger('loadtester')
        self.main_logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        self.main_logger.handlers.clear()
        
        # File handler
        file_handler = logging.FileHandler(self.log_files['main'], encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.main_logger.addHandler(file_handler)
        self.main_logger.addHandler(console_handler)
        
    def _setup_error_logger(self):
        """Setup dedicated error logger"""
        self.error_logger = logging.getLogger('loadtester.errors')
        self.error_logger.setLevel(logging.ERROR)
        
        # Remove existing handlers
        self.error_logger.handlers.clear()
        
        # File handler for errors only
        error_handler = logging.FileHandler(self.log_files['errors'], encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        
        # Detailed formatter for errors
        error_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s\n%(exc_info)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        
        self.error_logger.addHandler(error_handler)
        
        # Don't propagate to parent logger to avoid duplicate messages
        self.error_logger.propagate = False
        
    def _setup_component_loggers(self):
        """Setup loggers for specific components"""
        
        # Locust logger
        self.locust_logger = logging.getLogger('loadtester.locust')
        locust_handler = logging.FileHandler(self.log_files['locust'], encoding='utf-8')
        locust_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        self.locust_logger.addHandler(locust_handler)
        self.locust_logger.setLevel(logging.INFO)
        
        # Backend monitoring logger
        self.backend_logger = logging.getLogger('loadtester.backend')
        backend_handler = logging.FileHandler(self.log_files['backend'], encoding='utf-8')
        backend_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        self.backend_logger.addHandler(backend_handler)
        self.backend_logger.setLevel(logging.INFO)
        
        # Reports logger
        self.reports_logger = logging.getLogger('loadtester.reports')
        reports_handler = logging.FileHandler(self.log_files['reports'], encoding='utf-8')
        reports_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        self.reports_logger.addHandler(reports_handler)
        self.reports_logger.setLevel(logging.INFO)
    
    def get_logger(self, component: str = 'main') -> logging.Logger:
        """Get logger for specific component"""
        # Ensure logging is setup before returning logger
        if self.main_logger is None:
            self.setup_logging()
            
        logger_map = {
            'main': self.main_logger,
            'errors': self.error_logger,
            'locust': self.locust_logger,
            'backend': self.backend_logger,
            'reports': self.reports_logger
        }
        return logger_map.get(component, self.main_logger)
    
    def log_error(self, error: Exception, context: str = "", **kwargs):
        """Log error with context and additional information"""
        error_msg = f"Error in {context}: {str(error)}"
        
        # Add any additional context
        for key, value in kwargs.items():
            error_msg += f"\n  {key}: {value}"
        
        self.error_logger.error(error_msg, exc_info=True)
        
        # Also log to main logger at warning level
        self.main_logger.warning(f"Error occurred in {context}: {str(error)}")
    
    def log_test_start(self, test_config: dict):
        """Log test start with configuration"""
        self.main_logger.info("=" * 60)
        self.main_logger.info("LOAD TEST STARTING")
        self.main_logger.info("=" * 60)
        
        for key, value in test_config.items():
            self.main_logger.info(f"{key}: {value}")
        
        self.main_logger.info("=" * 60)
    
    def log_test_complete(self, results: dict):
        """Log test completion with summary"""
        self.main_logger.info("=" * 60)
        self.main_logger.info("LOAD TEST COMPLETE")
        self.main_logger.info("=" * 60)
        
        for key, value in results.items():
            self.main_logger.info(f"{key}: {value}")
        
        self.main_logger.info(f"Logs saved to: {self.run_logs_dir}")
        self.main_logger.info("=" * 60)
    
    def get_log_directory(self) -> Path:
        """Get current log directory path"""
        return self.run_logs_dir
    
    @classmethod
    def get_instance(cls) -> 'LoadTestLogger':
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance


def get_logger(component: str = 'main') -> logging.Logger:
    """Convenience function to get a logger"""
    return LoadTestLogger.get_instance().get_logger(component)


def log_error(error: Exception, context: str = "", **kwargs):
    """Convenience function to log errors"""
    LoadTestLogger.get_instance().log_error(error, context, **kwargs)


# Don't initialize at import time to avoid multiple initializations
# The singleton will be created on first use via get_instance()