#!/usr/bin/env python3
"""
Recovery test - Test system behavior after failures and recovery
"""
import asyncio
import subprocess
import json
import time
import httpx
from datetime import datetime
import argparse

class RecoveryTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]
        self.jwt_token = self.config["jwt_token"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}

    async def run_recovery_test(self):
        """Run comprehensive recovery tests"""
        print("🔄 Starting recovery tests...")
        
        tests = [
            ("Network Recovery", self._test_network_recovery),
            ("High Load Recovery", self._test_high_load_recovery),
            ("Connection Pool Recovery", self._test_connection_pool_recovery),
            ("JWT Token Recovery", self._test_jwt_recovery)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func()
            result["test_name"] = test_name
            results.append(result)
            
            # Wait between tests
            await asyncio.sleep(30)
        
        await self._save_recovery_results(results)

    async def _test_network_recovery(self):
        """Test recovery after network issues"""
        print("   Testing network interruption recovery...")
        
        # Baseline test
        baseline = await self._health_check()
        
        # Simulate network issues with very short timeout
        recovery_times = []
        
        for attempt in range(5):
            print(f"   Attempt {attempt + 1}: Testing with network stress...")
            
            start_time = time.time()
            
            # Make requests with very short timeout to simulate network issues
            async with httpx.AsyncClient(timeout=0.1) as client:
                try:
                    await client.get(f"{self.base_url}/health", headers=self.headers)
                except:
                    pass  # Expected to fail
            
            # Now test normal recovery
            recovery_success = await self._health_check()
            recovery_time = time.time() - start_time
            
            if recovery_success:
                recovery_times.append(recovery_time)
                print(f"   ✅ Recovered in {recovery_time:.2f}s")
            else:
                print(f"   ❌ Failed to recover")
        
        return {
            "baseline_healthy": baseline,
            "recovery_attempts": len(recovery_times),
            "successful_recoveries": len(recovery_times),
            "avg_recovery_time": sum(recovery_times) / len(recovery_times) if recovery_times else 0,
            "max_recovery_time": max(recovery_times) if recovery_times else 0
        }

    async def _test_high_load_recovery(self):
        """Test recovery after high load"""
        print("   Testing high load recovery...")
        
        # Baseline
        baseline = await self._health_check()
        
        # Apply high load
        print("   Applying high load (500 users for 60 seconds)...")
        load_config = self.config.copy()
        load_config['num_threads'] = 500
        
        temp_config = "temp_recovery_load.json"
        with open(temp_config, 'w') as f:
            json.dump(load_config, f, indent=2)
        
        try:
            # Run high load test
            subprocess.run([
                "python", "-m", "src.main",
                "-c", temp_config,
                "--skip-jwt-validation"
            ], timeout=90, capture_output=True)
        except subprocess.TimeoutExpired:
            pass  # Expected for high load
        finally:
            import os
            if os.path.exists(temp_config):
                os.remove(temp_config)
        
        # Test recovery
        recovery_start = time.time()
        recovery_attempts = 0
        max_attempts = 10
        
        while recovery_attempts < max_attempts:
            await asyncio.sleep(5)
            recovery_attempts += 1
            
            if await self._health_check():
                recovery_time = time.time() - recovery_start
                print(f"   ✅ System recovered in {recovery_time:.2f}s after {recovery_attempts} attempts")
                return {
                    "baseline_healthy": baseline,
                    "recovery_successful": True,
                    "recovery_time": recovery_time,
                    "recovery_attempts": recovery_attempts
                }
        
        print("   ❌ System failed to recover within time limit")
        return {
            "baseline_healthy": baseline,
            "recovery_successful": False,
            "recovery_time": time.time() - recovery_start,
            "recovery_attempts": recovery_attempts
        }

    async def _test_connection_pool_recovery(self):
        """Test connection pool exhaustion recovery"""
        print("   Testing connection pool recovery...")
        
        baseline = await self._health_check()
        
        # Exhaust connection pool
        print("   Exhausting connection pool...")
        tasks = []
        
        async def long_request():
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    await client.get(f"{self.base_url}/health", headers=self.headers)
                    await asyncio.sleep(10)  # Hold connection
            except:
                pass
        
        # Create many concurrent long-running requests
        for _ in range(100):
            tasks.append(asyncio.create_task(long_request()))
        
        # Wait a bit for connections to be established
        await asyncio.sleep(5)
        
        # Test if new connections can be made
        recovery_start = time.time()
        recovery_success = await self._health_check()
        recovery_time = time.time() - recovery_start
        
        # Cancel long-running tasks
        for task in tasks:
            task.cancel()
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            "baseline_healthy": baseline,
            "recovery_during_exhaustion": recovery_success,
            "recovery_time": recovery_time
        }

    async def _test_jwt_recovery(self):
        """Test JWT token refresh/recovery"""
        print("   Testing JWT token recovery...")
        
        baseline = await self._health_check()
        
        # Test with invalid token
        invalid_headers = {"X-CGPT-AUTHORIZATION": "Bearer invalid_token"}
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health", headers=invalid_headers)
                invalid_token_works = response.status_code == 200
            except:
                invalid_token_works = False
        
        # Test recovery with valid token
        recovery_success = await self._health_check()
        
        return {
            "baseline_healthy": baseline,
            "invalid_token_rejected": not invalid_token_works,
            "valid_token_recovery": recovery_success
        }

    async def _health_check(self) -> bool:
        """Perform health check"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/health", headers=self.headers)
                return response.status_code == 200
        except:
            return False

    async def _save_recovery_results(self, results: list):
        """Save recovery test results"""
        filename = f"recovery_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "test_type": "recovery_test",
            "test_date": datetime.now().isoformat(),
            "results": results,
            "summary": {
                "total_tests": len(results),
                "passed_tests": sum(1 for r in results if r.get("recovery_successful", True))
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 Recovery test results saved to {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Recovery test after failures")
    parser.add_argument("--config", default="config.global.json", help="Configuration file")
    
    args = parser.parse_args()
    
    tester = RecoveryTester(args.config)
    await tester.run_recovery_test()

if __name__ == "__main__":
    asyncio.run(main())