#!/usr/bin/env python3
"""
Simple Load Test using the enhanced framework
============================================

This is a simplified test that works with your current setup.
Run with: locust -f simple_test.py --host=https://mia-ta.lacaisse.com
"""

import os
import json
import random
from locust import HttpUser, task, between

# Load configuration
CONFIG_PATH = os.environ.get("CONFIG_PATH", "config.global.json")
with open(CONFIG_PATH, "r", encoding="utf-8") as f:
    config = json.load(f)

endpoints = config.get("endpoints", [])
jwt_token = config.get("jwt_token", "")
verify_ssl = config.get("verify_ssl", True)


class EnhancedAPIUser(HttpUser):
    """Enhanced API user with JWT authentication"""
    wait_time = between(1, 3)
    
    def on_start(self):
        """Initialize user with JWT authentication"""
        self.jwt_token = jwt_token
        self.endpoints = endpoints
        self.user_id = f"user_{random.randint(1000, 9999)}"
        self.auth_headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        
        print(f"🚀 User {self.user_id} starting with {len(self.endpoints)} endpoints")
        
        # Create session for andoc context
        self.create_session("andoc")
    
    def create_session(self, context):
        """Create a session for the specified context"""
        endpoint = f"/{context}/session/new"
        
        with self.client.post(
            endpoint, 
            headers=self.auth_headers, 
            catch_response=True,
            verify=verify_ssl,
            name=f"create_session_{context}"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.session_id = data.get("session_id")
                    print(f"✅ Session created: {self.session_id}")
                    response.success()
                except:
                    response.failure("Invalid session response")
            else:
                response.failure(f"Session creation failed: {response.status_code}")
    
    @task(5)
    def health_check(self):
        """Health check endpoint"""
        with self.client.get(
            "/health", 
            headers=self.auth_headers,
            catch_response=True,
            verify=verify_ssl,
            name="health_check"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(10)
    def send_message(self):
        """Send a message to the chat endpoint"""
        if not hasattr(self, 'session_id') or not self.session_id:
            self.create_session("andoc")
            return
        
        messages = [
            "Bonjour, comment puis-je vous aider?",
            "J'ai une question sur les congés",
            "Comment faire une demande de formation?",
            "Quels sont mes avantages sociaux?",
            "Comment mettre à jour mes informations personnelles?"
        ]
        
        message = random.choice(messages)
        endpoint = "/andoc/chat/send"
        
        payload = {
            "message": message,
            "session_id": self.session_id
        }
        
        with self.client.post(
            endpoint,
            headers=self.auth_headers,
            json=payload,
            catch_response=True,
            verify=verify_ssl,
            name="send_message"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Message failed: {response.status_code}")
    
    @task(3)
    def get_chat_history(self):
        """Get chat history"""
        if not hasattr(self, 'session_id') or not self.session_id:
            return
        
        endpoint = "/andoc/chat/history"
        params = {
            "session_id": self.session_id,
            "limit": 10
        }
        
        with self.client.get(
            endpoint,
            headers=self.auth_headers,
            params=params,
            catch_response=True,
            verify=verify_ssl,
            name="get_history"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"History failed: {response.status_code}")
    
    @task(2)
    def run_configured_endpoints(self):
        """Run endpoints from configuration"""
        for endpoint_config in self.endpoints:
            url = endpoint_config.get("url", "/")
            method = endpoint_config.get("method", "GET").upper()
            data = endpoint_config.get("data", {})
            endpoint_id = endpoint_config.get("id", url.replace("/", "_"))
            
            try:
                if method == "GET":
                    with self.client.get(
                        url, 
                        headers=self.auth_headers, 
                        params=data,
                        catch_response=True, 
                        verify=verify_ssl,
                        name=endpoint_id
                    ) as response:
                        self._handle_response(response, endpoint_config)
                        
                elif method == "POST":
                    with self.client.post(
                        url, 
                        headers=self.auth_headers, 
                        json=data,
                        catch_response=True, 
                        verify=verify_ssl,
                        name=endpoint_id
                    ) as response:
                        self._handle_response(response, endpoint_config)
                        
            except Exception as e:
                print(f"Exception calling {url}: {e}")
    
    def _handle_response(self, response, endpoint_config):
        """Handle response from endpoint"""
        if response.status_code >= 400:
            response.failure(f"HTTP {response.status_code}: {response.text[:100]}")
        else:
            response.success()
    
    def on_stop(self):
        """Cleanup when user stops"""
        print(f"🏁 User {self.user_id} stopping")


# For compatibility with the enhanced framework
class LegacyCompatibilityUser(EnhancedAPIUser):
    """Maintains compatibility with existing configurations"""
    weight = 1


# Define user classes for Locust
user_classes = [EnhancedAPIUser, LegacyCompatibilityUser]
