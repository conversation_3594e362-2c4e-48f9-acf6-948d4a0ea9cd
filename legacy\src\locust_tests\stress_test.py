"""
Stress Test
===========

Tests the application beyond normal capacity to identify breaking points.
Uses aggressive user behavior with minimal wait times and high concurrency.
"""

import os
import json
import random
from locust import task, between
from src.locust_framework import AuthenticatedUser


class AggressiveUser(AuthenticatedUser):
    """Aggressive user behavior for stress testing"""
    
    wait_time = between(0.1, 1.0)  # Very short wait times
    weight = 5  # High weight for stress
    
    def on_start(self):
        super().on_start()
        
        # Load configuration
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            self.config_data = json.load(f)
        
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
        
        # Track errors for this user
        self.error_count = 0
        self.request_count = 0
    
    @task(15)
    def rapid_fire_messages(self):
        """Send messages rapidly to stress the system"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        # Stress-inducing messages
        stress_messages = [
            "Test message " + str(random.randint(1, 10000)),
            "Stress test query",
            "Load testing in progress",
            "System capacity test",
            "Performance validation",
            "Concurrent user simulation"
        ]
        
        message = random.choice(stress_messages)
        endpoint = f"/{self.active_context}/chat/send"
        
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id
        }
        
        self.request_count += 1
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name=f"stress_{self.active_context}_message") as response:
            if response.status_code == 200:
                response.success()
            else:
                self.error_count += 1
                response.failure(f"Stress message failed: {response.status_code}")
    
    @task(10)
    def concurrent_health_checks(self):
        """Flood health endpoint"""
        with self.authenticated_request("GET", "/health", 
                                      name="stress_health_check") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(8)
    def session_creation_flood(self):
        """Create many sessions to stress session management"""
        context = random.choice(self.contexts)
        endpoint = f"/{context}/session/new"
        
        with self.authenticated_request("POST", endpoint,
                                      name=f"stress_session_creation") as response:
            if response.status_code == 200:
                response.success()
                # Don't store all sessions, just test creation
            else:
                response.failure(f"Session creation failed: {response.status_code}")
    
    @task(5)
    def large_payload_requests(self):
        """Send large payloads to stress data processing"""
        large_message = "This is a very long message " * 100  # ~3KB message
        
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        endpoint = f"/{self.active_context}/chat/send"
        payload = {
            "message": large_message,
            "session_id": self.session_manager.session_data.session_id,
            "metadata": {
                "test_type": "stress",
                "payload_size": "large",
                "timestamp": str(random.randint(1000000, 9999999))
            }
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="stress_large_payload") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Large payload failed: {response.status_code}")
    
    @task(3)
    def rapid_context_switching(self):
        """Rapidly switch between contexts"""
        new_context = random.choice(self.contexts)
        self.active_context = new_context
        self.create_user_session(new_context)
    
    @task(2)
    def concurrent_history_requests(self):
        """Make multiple history requests"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        endpoint = f"/{self.active_context}/chat/history"
        params = {
            "session_id": self.session_manager.session_data.session_id,
            "limit": 100  # Request large history
        }
        
        with self.authenticated_request("GET", endpoint, params=params,
                                      name="stress_history_large") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"History request failed: {response.status_code}")


class DatabaseStressUser(AuthenticatedUser):
    """User focused on stressing database operations"""
    
    wait_time = between(0.1, 0.5)  # Minimal wait time
    weight = 3
    
    def on_start(self):
        super().on_start()
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
    
    @task(10)
    def concurrent_session_operations(self):
        """Stress session database operations"""
        operations = [
            ("POST", f"/{self.active_context}/session/new", {}, "db_session_create"),
            ("GET", f"/{self.active_context}/session/list", {}, "db_session_list"),
            ("DELETE", f"/{self.active_context}/session/cleanup", {}, "db_session_cleanup")
        ]
        
        method, url, data, name = random.choice(operations)
        
        with self.authenticated_request(method, url, json=data, name=name) as response:
            if response.status_code in [200, 201, 204]:
                response.success()
            else:
                response.failure(f"DB operation failed: {response.status_code}")
    
    @task(8)
    def message_history_stress(self):
        """Stress message storage and retrieval"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        # Alternate between storing and retrieving
        if random.choice([True, False]):
            # Store message
            endpoint = f"/{self.active_context}/chat/send"
            payload = {
                "message": f"DB stress test {random.randint(1, 100000)}",
                "session_id": self.session_manager.session_data.session_id
            }
            
            with self.authenticated_request("POST", endpoint, json=payload,
                                          name="db_message_store") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Message store failed: {response.status_code}")
        else:
            # Retrieve history
            endpoint = f"/{self.active_context}/chat/history"
            params = {
                "session_id": self.session_manager.session_data.session_id,
                "limit": random.randint(10, 100)
            }
            
            with self.authenticated_request("GET", endpoint, params=params,
                                          name="db_history_retrieve") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"History retrieve failed: {response.status_code}")


class ConnectionPoolStressUser(AuthenticatedUser):
    """User designed to stress connection pools and network resources"""
    
    wait_time = between(0.05, 0.2)  # Very aggressive timing
    weight = 2
    
    @task(20)
    def rapid_requests(self):
        """Make rapid requests to exhaust connection pools"""
        endpoints = ["/health", "/status", "/metrics"]
        endpoint = random.choice(endpoints)
        
        with self.authenticated_request("GET", endpoint,
                                      name="connection_stress") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Connection stress failed: {response.status_code}")
    
    @task(5)
    def long_running_requests(self):
        """Simulate requests that might hold connections longer"""
        endpoint = f"/{random.choice(['andoc', 'talperftraitement'])}/chat/send"
        
        # Large payload that might take time to process
        payload = {
            "message": "Process this complex query: " + "data " * 500,
            "session_id": f"stress_session_{random.randint(1, 1000)}"
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="long_running_request") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Long request failed: {response.status_code}")


class ErrorInducingUser(AuthenticatedUser):
    """User that intentionally triggers error conditions"""
    
    wait_time = between(0.5, 2.0)
    weight = 1  # Lower weight as these are error cases
    
    @task(5)
    def invalid_requests(self):
        """Make requests with invalid data"""
        error_scenarios = [
            ("POST", "/andoc/chat/send", {"invalid": "data"}, "error_invalid_payload"),
            ("GET", "/nonexistent/endpoint", {}, "error_404"),
            ("POST", "/andoc/session/new", {"malformed": True}, "error_malformed"),
            ("DELETE", "/protected/resource", {}, "error_unauthorized")
        ]
        
        method, url, data, name = random.choice(error_scenarios)
        
        with self.authenticated_request(method, url, json=data, name=name) as response:
            # For error-inducing tests, we expect failures
            if response.status_code >= 400:
                response.success()  # Expected error
            else:
                response.failure(f"Expected error but got: {response.status_code}")
    
    @task(3)
    def malformed_auth(self):
        """Test with malformed authentication"""
        # Temporarily modify headers
        original_headers = self.auth_headers.copy()
        self.auth_headers["X-CGPT-AUTHORIZATION"] = "Bearer invalid_token"
        
        with self.authenticated_request("GET", "/health", name="error_bad_auth") as response:
            if response.status_code == 401:
                response.success()  # Expected unauthorized
            else:
                response.failure(f"Expected 401 but got: {response.status_code}")
        
        # Restore headers
        self.auth_headers = original_headers


# Define user classes for stress testing
user_classes = [AggressiveUser, DatabaseStressUser, ConnectionPoolStressUser, ErrorInducingUser]
