#!/usr/bin/env python3
"""
Diagnostic Test - Check Available Endpoints
==========================================

This test helps identify what endpoints are available and working.
"""

import os
import json
import requests
from urllib3 import disable_warnings
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
disable_warnings(InsecureRequestWarning)

# Load configuration
CONFIG_PATH = os.environ.get("CONFIG_PATH", "config.global.json")
with open(CONFIG_PATH, "r", encoding="utf-8") as f:
    config = json.load(f)

base_url = config.get("base_url", "https://mia-ta.lacaisse.com")
jwt_token = config.get("jwt_token", "")
verify_ssl = config.get("verify_ssl", True)

headers = {"X-CGPT-AUTHORIZATION": f"Bearer {jwt_token}"}

def test_endpoint(method, endpoint, data=None):
    """Test a single endpoint"""
    url = f"{base_url}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, verify=verify_ssl, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, verify=verify_ssl, timeout=10)
        
        print(f"{method.upper()} {endpoint}")
        print(f"  Status: {response.status_code}")
        print(f"  Response Time: {response.elapsed.total_seconds()*1000:.0f}ms")
        
        if response.status_code < 400:
            print(f"  ✅ SUCCESS")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"  Response: {response.text[:100]}...")
        else:
            print(f"  ❌ FAILED: {response.text[:100]}")
        
        print()
        return response.status_code < 400
        
    except Exception as e:
        print(f"{method.upper()} {endpoint}")
        print(f"  ❌ ERROR: {e}")
        print()
        return False

def main():
    print("🔍 DIAGNOSTIC TEST - API Endpoint Discovery")
    print("=" * 50)
    print(f"Base URL: {base_url}")
    print(f"JWT Token: {'✅ Present' if jwt_token else '❌ Missing'}")
    print(f"SSL Verification: {verify_ssl}")
    print()
    
    # Test basic endpoints
    endpoints_to_test = [
        ("GET", "/health"),
        ("GET", "/status"),
        ("GET", "/version"),
        ("GET", "/"),
        ("GET", "/andoc"),
        ("POST", "/andoc/session/new"),
        ("POST", "/andoc/session/new", {}),
        ("GET", "/andoc/chat/history"),
        ("POST", "/andoc/chat/send", {"message": "test"}),
        ("GET", "/talperftraitement"),
        ("POST", "/talperftraitement/session/new"),
    ]
    
    successful = 0
    total = len(endpoints_to_test)
    
    for method, endpoint, *data in endpoints_to_test:
        payload = data[0] if data else None
        if test_endpoint(method, endpoint, payload):
            successful += 1
    
    print("=" * 50)
    print(f"📊 SUMMARY: {successful}/{total} endpoints successful")
    print()
    
    # Test configured endpoints from config
    configured_endpoints = config.get("endpoints", [])
    if configured_endpoints:
        print("🔧 TESTING CONFIGURED ENDPOINTS")
        print("-" * 30)
        
        for endpoint_config in configured_endpoints:
            url = endpoint_config.get("url", "/")
            method = endpoint_config.get("method", "GET")
            data = endpoint_config.get("data", {})
            
            test_endpoint(method, url, data if data else None)

if __name__ == "__main__":
    main()
