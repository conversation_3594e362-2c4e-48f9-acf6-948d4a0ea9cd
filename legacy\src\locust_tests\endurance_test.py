"""
Endurance Test (Soak Test)
=========================

Tests the application's stability over extended periods.
Identifies memory leaks, resource exhaustion, and gradual performance degradation.
"""

import os
import json
import random
import time
from datetime import datetime, timedelta
from locust import task, between, events
from src.locust_framework import AuthenticatedUser


class EnduranceUser(AuthenticatedUser):
    """User designed for long-running endurance testing"""
    
    wait_time = between(3, 10)  # Moderate pace for sustainability
    weight = 3
    
    def __init__(self, environment):
        super().__init__(environment)
        self.start_time = datetime.now()
        self.session_refresh_interval = timedelta(hours=2)  # Refresh session every 2 hours
        self.last_session_refresh = self.start_time
        self.request_count = 0
        self.error_count = 0
        
    def on_start(self):
        super().on_start()
        
        # Load configuration
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            self.config_data = json.load(f)
        
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
        
        # Track user lifecycle
        self.lifecycle_stage = "startup"
        self.session_count = 0
    
    def check_session_refresh(self):
        """Check if session needs refreshing for long-running test"""
        now = datetime.now()
        if now - self.last_session_refresh > self.session_refresh_interval:
            self.create_user_session(self.active_context)
            self.last_session_refresh = now
            self.session_count += 1
    
    @task(10)
    def sustained_messaging(self):
        """Sustained messaging over long periods"""
        self.check_session_refresh()
        
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        # Vary message complexity over time to simulate real usage
        runtime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
        
        if runtime_hours < 1:
            # First hour: simple queries
            messages = [
                "Bonjour",
                "Comment ça va?",
                "Aide rapide",
                "Information de base"
            ]
        elif runtime_hours < 4:
            # Hours 1-4: normal complexity
            messages = [
                "J'ai une question sur les congés",
                "Comment faire une demande de formation?",
                "Informations sur les bénéfices",
                "Procédure de télétravail"
            ]
        else:
            # After 4 hours: complex queries
            messages = [
                "J'ai besoin d'une explication détaillée sur la politique de remboursement des frais de déplacement",
                "Pouvez-vous m'aider à comprendre les différentes options d'assurance disponibles?",
                "Quelle est la procédure complète pour une demande de congé parental?",
                "Comment puis-je accéder à tous mes documents RH historiques?"
            ]
        
        message = random.choice(messages)
        endpoint = f"/{self.active_context}/chat/send"
        
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id,
            "runtime_hours": runtime_hours,
            "session_count": self.session_count
        }
        
        self.request_count += 1
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="endurance_message") as response:
            if response.status_code == 200:
                response.success()
            else:
                self.error_count += 1
                response.failure(f"Endurance message failed: {response.status_code}")
    
    @task(5)
    def periodic_health_monitoring(self):
        """Regular health checks throughout the test"""
        with self.authenticated_request("GET", "/health", 
                                      name="endurance_health") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(3)
    def session_lifecycle_management(self):
        """Manage session lifecycle over long periods"""
        operations = [
            ("GET", f"/{self.active_context}/session/status", 
             {"session_id": getattr(self.session_manager.session_data, 'session_id', '')}, 
             "session_status"),
            ("POST", f"/{self.active_context}/session/heartbeat", 
             {"session_id": getattr(self.session_manager.session_data, 'session_id', '')}, 
             "session_heartbeat"),
            ("GET", f"/{self.active_context}/session/info", 
             {"session_id": getattr(self.session_manager.session_data, 'session_id', '')}, 
             "session_info")
        ]
        
        method, url, data, name = random.choice(operations)
        
        with self.authenticated_request(method, url, json=data if method == "POST" else None,
                                      params=data if method == "GET" else None,
                                      name=f"endurance_{name}") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Session operation failed: {response.status_code}")
    
    @task(2)
    def data_accumulation_test(self):
        """Test data accumulation over time"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        # Request chat history - this should grow over time
        endpoint = f"/{self.active_context}/chat/history"
        params = {
            "session_id": self.session_manager.session_data.session_id,
            "limit": 50
        }
        
        with self.authenticated_request("GET", endpoint, params=params,
                                      name="endurance_history_growth") as response:
            if response.status_code == 200:
                response.success()
                # Log history size for monitoring
                try:
                    data = response.json()
                    history_size = len(data.get('messages', []))
                    # This could be logged to external monitoring
                except:
                    pass
            else:
                response.failure(f"History request failed: {response.status_code}")
    
    @task(1)
    def resource_intensive_operation(self):
        """Occasionally perform resource-intensive operations"""
        # Large document upload simulation
        large_content = "Large document content " * 1000  # ~25KB
        
        endpoint = f"/{self.active_context}/document/analyze"
        payload = {
            "content": large_content,
            "session_id": getattr(self.session_manager.session_data, 'session_id', ''),
            "analysis_type": "comprehensive"
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="endurance_resource_intensive") as response:
            if response.status_code in [200, 201, 202]:
                response.success()
            else:
                response.failure(f"Resource intensive operation failed: {response.status_code}")


class MemoryLeakDetectionUser(AuthenticatedUser):
    """User specifically designed to detect memory leaks"""
    
    wait_time = between(2, 5)
    weight = 2
    
    def __init__(self, environment):
        super().__init__(environment)
        self.large_data_counter = 0
        self.session_counter = 0
        
    def on_start(self):
        super().on_start()
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
    
    @task(8)
    def memory_accumulation_test(self):
        """Operations that might cause memory accumulation"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        # Create progressively larger payloads
        self.large_data_counter += 1
        data_size = min(self.large_data_counter * 100, 5000)  # Cap at 5KB
        
        large_message = f"Memory test message {self.large_data_counter}: " + "data " * data_size
        
        endpoint = f"/{self.active_context}/chat/send"
        payload = {
            "message": large_message,
            "session_id": self.session_manager.session_data.session_id,
            "data_size": data_size,
            "counter": self.large_data_counter
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="memory_leak_test") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Memory test failed: {response.status_code}")
    
    @task(5)
    def session_creation_cycle(self):
        """Repeatedly create and abandon sessions"""
        self.session_counter += 1
        
        # Create new session
        new_context = random.choice(self.contexts)
        endpoint = f"/{new_context}/session/new"
        
        with self.authenticated_request("POST", endpoint,
                                      name="memory_session_create") as response:
            if response.status_code == 200:
                response.success()
                # Don't store the session - let it be garbage collected
            else:
                response.failure(f"Session creation failed: {response.status_code}")
    
    @task(3)
    def cache_pollution_test(self):
        """Operations that might pollute caches"""
        # Request unique data that shouldn't be cached efficiently
        unique_queries = [
            f"Unique query {random.randint(1, 1000000)}",
            f"Timestamp query {datetime.now().isoformat()}",
            f"Random data {random.random()}"
        ]
        
        query = random.choice(unique_queries)
        endpoint = f"/{self.active_context}/search"
        
        payload = {
            "query": query,
            "session_id": getattr(self.session_manager.session_data, 'session_id', ''),
            "cache_buster": random.randint(1, 1000000)
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="cache_pollution_test") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Cache test failed: {response.status_code}")


class LongRunningSessionUser(AuthenticatedUser):
    """User that maintains very long-running sessions"""
    
    wait_time = between(5, 15)  # Slower pace
    weight = 1
    
    def __init__(self, environment):
        super().__init__(environment)
        self.session_age = 0
        self.messages_in_session = 0
        
    def on_start(self):
        super().on_start()
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
    
    @task(10)
    def long_session_interaction(self):
        """Interactions within a very long session"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        self.messages_in_session += 1
        self.session_age += 1
        
        # Reference previous messages in the long session
        message = f"Message {self.messages_in_session} in this long session. Previous context matters."
        
        endpoint = f"/{self.active_context}/chat/send"
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id,
            "session_age": self.session_age,
            "message_count": self.messages_in_session
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="long_session_message") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Long session message failed: {response.status_code}")
    
    @task(2)
    def session_maintenance(self):
        """Maintain the long-running session"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            return
        
        # Keep session alive
        endpoint = f"/{self.active_context}/session/keepalive"
        payload = {
            "session_id": self.session_manager.session_data.session_id,
            "age": self.session_age
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name="session_keepalive") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Session keepalive failed: {response.status_code}")


# Define user classes for endurance testing
user_classes = [EnduranceUser, MemoryLeakDetectionUser, LongRunningSessionUser]
