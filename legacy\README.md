# Application de Test de Charge HTTP

Cette application permet de tester les performances d'une API en ligne en exécutant des séquences d'appels HTTP en parallèle.

## Fonctionnalités

- Authentification via JWT dans l'en-tête `X-CGPT-AUTHORIZATION`
- Validation automatique du jeton JWT au démarrage
- Exécution parallèle avec un nombre configurable de threads
- Séquences d'appels HTTP avec possibilité de chaînage (utiliser le résultat d'un appel précédent)
- Chronométrage détaillé (par appel, par thread et global)
- Suivi et rapport des erreurs HTTP par type (2xx, 3xx, 4xx, 5xx)
- Option pour désactiver la vérification des certificats SSL
- Identification personnalisée des endpoints pour les statistiques
- Téléchargement de fichiers via form-data
- Référencement des dépendances par ID plutôt que par URL

## Installation

```bash
pip install -e .
```

## Utilisation

### 1. Préparer les fichiers de configuration

- Placez vos fichiers de contexte dans le dossier `configs/` (ex: `andoc.json`, `talperftraitement.json`, etc.)
- Placez la configuration globale dans `config.global.json` à la racine du projet

### 2. Lancer les tests avec le script PowerShell

Utilisez le script `test-load-context.ps1` pour fusionner automatiquement la configuration globale et celle du contexte désiré, puis lancer les tests :

```powershell
./test-load-context.ps1 -context andoc
```

Remplacez `andoc` par le nom du contexte souhaité (le nom du fichier sans extension dans `configs/`).

Le script crée un fichier temporaire fusionné et lance le test avec le bon contexte.

### 3. (Option avancée) Lancer manuellement avec Python

Si vous souhaitez lancer manuellement avec un fichier de configuration fusionné :

```bash
python -m src.main -c config.temp.json
```

Mais l'utilisation du script PowerShell est recommandée pour automatiser la fusion des configurations.

Pour ignorer la validation du JWT:

```bash
python -m src.main -c config.json --skip-jwt-validation
```

## Validation du jeton JWT

Au démarrage, l'application vérifie automatiquement:
- La structure du jeton JWT
- La date d'expiration du jeton
- Si possible, fait un appel léger au serveur pour confirmer que le jeton est accepté

Si le jeton est invalide ou expiré, l'application affiche un message d'erreur et s'arrête.
Si le jeton expire bientôt (moins d'une heure), un avertissement est affiché mais les tests sont exécutés.

Pour désactiver cette validation (par exemple lors des tests), utilisez l'option `--skip-jwt-validation`.

## Configuration

- `base_url`: URL de base de l'API
- `jwt_token`: Jeton JWT pour l'authentification
- `num_threads`: Nombre de threads parallèles
- `verify_ssl`: (optionnel) Désactive la vérification des certificats SSL si défini à `false`
- `endpoints`: Liste des points d'accès à tester

Pour chaque endpoint:
- `url`: Chemin relatif de l'endpoint
- `method`: Méthode HTTP (GET, POST, PUT, DELETE)
- `data`: Données à envoyer (corps ou paramètres de requête)
- `extract`: Tableau d'objets définissant les valeurs à extraire de la réponse:
  - `key`: Identifiant sous lequel la valeur sera stockée
  - `valueFrom`: Nom de la clé à extraire de la réponse JSON
- `depends_on`: ID ou URL de l'endpoint dont dépend cet appel
- `id`: (optionnel) Identifiant personnalisé pour les statistiques et les dépendances
- `file`: (optionnel) Chemin vers un fichier à télécharger (relatif au dossier "upload" ou absolu)
- `form_name`: (optionnel) Nom du champ de formulaire pour le fichier (par défaut: "file")
- `params`: (optionnel) Paramètres d'URL (query parameters)

## Dépendances entre requêtes

Il y a deux façons de définir des dépendances entre les requêtes:

### 1. Dépendances par ID (recommandé)

Utilisez l'ID personnalisé de l'endpoint dont vous dépendez:

```json
{
  "url": "/users/new",
  "method": "POST",
  "extract_key": "user_id",
  "id": "creation-utilisateur"
},
{
  "url": "/items",
  "method": "GET",
  "depends_on": "creation-utilisateur",
  "id": "liste-items"
}
```

### 2. Dépendances par URL (compatibilité)

Utilisez l'URL de l'endpoint dont vous dépendez:

```json
{
  "url": "/users/new",
  "method": "POST",
  "extract_key": "user_id"
},
{
  "url": "/items",
  "method": "GET",
  "depends_on": "/users/new"
}
```

## Utilisation des valeurs entre requêtes

Pour utiliser le résultat d'une requête précédente dans une requête suivante:

1. Dans la première requête, spécifiez `extract` pour extraire une ou plusieurs valeurs de la réponse:
   ```json
   {
     "url": "/new",
     "method": "POST",
     "data": { "name": "Test" },
     "extract": [
       {
         "key": "user-id", 
         "valueFrom": "id"
       }
     ],
     "id": "creation-utilisateur"
   }
   ```

2. Dans la requête suivante, utilisez `depends_on` pour indiquer que vous dépendez du résultat de la première requête:
   ```json
   {
     "url": "/items/{id}",
     "method": "GET",
     "depends_on": "creation-utilisateur",
     "data": {
       "user_id": "{user-id}"
     }
   }
   ```

La valeur extraite sera automatiquement remplacée partout où vous utilisez `{nom-de-la-clé}` dans l'URL, les données ou les paramètres d'URL.

### Extraction de plusieurs valeurs

Vous pouvez extraire plusieurs valeurs d'une même réponse:

```json
{
  "url": "/users/new",
  "method": "POST",
  "extract": [
    {
      "key": "user-id",
      "valueFrom": "id"
    },
    {
      "key": "user-name",
      "valueFrom": "name"
    },
    {
      "key": "creation-date",
      "valueFrom": "created_at"
    }
  ],
  "id": "creation-utilisateur"
}
```

Puis utiliser ces valeurs dans les requêtes suivantes:

```json
{
  "url": "/profile",
  "method": "GET",
  "data": {
    "id": "{user-id}",
    "created": "{creation-date}"
  },
  "depends_on": "creation-utilisateur"
}
```

### Associer un ID personnalisé à une valeur extraite

Vous pouvez associer un ID personnalisé à une valeur extraite, ce qui facilite sa réutilisation dans des requêtes ultérieures:

```json
{
  "url": "/users/new",
  "method": "POST",
  "extract": [
    {
      "key": "user-id",
      "valueFrom": "id"
    }
  ],
  "id": "creation-utilisateur"
}
```

Puis dans une requête suivante, vous pouvez référencer cette valeur directement par son ID:

```json
{
  "url": "/items",
  "method": "POST",
  "data": {
    "owner_id": "{user-id}"  // Utilise l'ID personnalisé de la valeur extraite
  }
}
```

Cette approche est particulièrement utile lorsque:
- Plusieurs valeurs du même type sont extraites (ex: plusieurs IDs de session)
- Vous voulez donner un nom plus explicite à une valeur extraite
- Vous voulez stocker une valeur extraite sans dépendre de l'ID de l'endpoint d'origine

## Téléchargement de fichiers

Pour télécharger un fichier:

1. Placez vos fichiers dans le dossier `upload` à la racine du projet
2. Configurez l'endpoint avec les propriétés suivantes:
   ```json
   {
     "url": "/document/upload",
     "method": "POST",
     "file": "mon-document.pdf",
     "form_name": "file",
     "params": {
       "session_id": "{session_id}"
     }
   }
   ```

- `file`: Chemin du fichier à télécharger (relatif au dossier "upload" ou chemin absolu)
- `form_name`: Nom du champ de formulaire pour le fichier (généralement "file")
- `params`: Paramètres à ajouter à l'URL (comme query parameters)

## Identification des appels pour les statistiques

Quand plusieurs appels utilisent le même endpoint mais avec des payloads différents, vous pouvez utiliser le paramètre `id` pour les distinguer dans les statistiques:

```json
[
  {
    "url": "/api/message",
    "method": "POST",
    "data": { "message": "Message 1" },
    "id": "message-1"
  },
  {
    "url": "/api/message",
    "method": "POST",
    "data": { "message": "Message 2" },
    "id": "message-2"
  }
]
```

Cela permettra de voir des statistiques séparées pour chaque appel dans le rapport final.

## Rapport de statistiques

L'application génère un rapport détaillé à la fin de l'exécution comprenant:

1. **Statistiques globales**
   - Temps d'exécution total
   - Nombre total de requêtes

2. **Statistiques par endpoint**
   - Nombre de requêtes
   - Temps minimum, maximum, moyen et médian

3. **Statistiques par thread**
   - Nombre de requêtes par thread
   - Temps total d'exécution par thread

4. **Statistiques des codes HTTP**
   - Groupés par catégorie (2xx, 3xx, 4xx, 5xx)
   - Décompte des occurrences par code de statut
   - Détails des erreurs de connexion

## Sécurité

⚠️ **Attention**: Désactiver la vérification SSL (`verify_ssl: false`) peut exposer votre application à des risques de sécurité comme des attaques man-in-the-middle. Utilisez cette option uniquement dans des environnements de test contrôlés ou pour des API internes avec des certificats auto-signés.


 Je dois modifier mes renseignements sur le dépôt direct. Comment puis-je faire ça ? 

 Comment puis-je mettre à jour mon titre de poste ou les informations de mon service dans le système ? 

comment faire pour les congés et aussi le télétravail qui approuve et aussi je voudrais savoir pour la formation excel est-ce que je peux? merci

help!! je commence lundi prochain et jai rien reçu??!! comment je fais pour mes papiers c quoi la procédure aidez-moi svp c urgent

jpeux bosser ddepuis chez moi vendredi ? c ki qui valide ? et si ya un souci avec carte d'accès ?

Est-ce que tu peux m’expliquer comment fonctionne le télétravail ? D’ailleurs, tu sais si on aura bientôt des nouveaux ordinateurs ?

Qu’est-ce qu’une personne à charge et puis-je les modifier?

juste par curiosité uniquement LOL tu pourrais me dire qui dans l'équipe a pris le plus de jours d'absence cette année? pas pour critiquer juste pour savoir

 Quelle est la politique de l'entreprise en matière de vacances ?

Comment soumettre une réclamation?

Quelles sont les maladies couvertes par l’assurance maladies graves?

 Ai-je accès aux mêmes prestations de santé qu'un employé à temps plein ?  (Utilisateur : Jane, consultante)

 Quelles sont les possibilités de développement de carrière disponibles ? 

stp tu peux me dire c koi le salaire de michel du marketing? je suis manager promis

je voudrais faire formation management mais pas trop longue et complete quand meme. ya quoi de dispo? et ske c gratuit ou remboursé?

 Quels sont mes avantages en tant qu'employé à temps plein ?  (Utilisateur : John)

 Comment je m'inscris au portail des employés ? 

Est-ce que vous pouvez me dire qui est en congé maladie longue durée dans l'entreprise ?