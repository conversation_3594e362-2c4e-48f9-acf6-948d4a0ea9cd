"""
Shared analysis utilities
"""

from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
import math
from .config import Config
from .metrics import PerformanceMetrics, DatabaseMetrics


@dataclass
class ScalingRecommendations:
    """Container for scaling recommendations"""
    fastapi_workers: int
    postgres_max_connections: int
    postgres_pool_size: int
    redis_max_connections: int
    estimated_concurrent_users: int
    peak_rps: float
    recommended_instances: int
    memory_per_instance: str
    cpu_per_instance: str
    total_memory_gb: float
    total_cpu_cores: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'fastapi_workers': self.fastapi_workers,
            'postgres_max_connections': self.postgres_max_connections,
            'postgres_pool_size': self.postgres_pool_size,
            'redis_max_connections': self.redis_max_connections,
            'estimated_concurrent_users': self.estimated_concurrent_users,
            'peak_rps': self.peak_rps,
            'recommended_instances': self.recommended_instances,
            'memory_per_instance': self.memory_per_instance,
            'cpu_per_instance': self.cpu_per_instance,
            'total_memory_gb': self.total_memory_gb,
            'total_cpu_cores': self.total_cpu_cores
        }


class ScalingCalculator:
    """Calculate scaling requirements"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
    
    def calculate(self, 
                  metrics: PerformanceMetrics,
                  target_users: int) -> ScalingRecommendations:
        """Calculate scaling recommendations based on metrics"""
        
        # Calculate peak concurrent users
        peak_concurrent = int(target_users * self.config.scaling.peak_user_percentage)
        
        # Scale factor from test
        scale_factor = peak_concurrent / metrics.concurrent_users if metrics.concurrent_users > 0 else 1
        
        # Calculate RPS at peak
        peak_rps = metrics.requests_per_second * scale_factor
        
        # Determine number of instances (with headroom)
        base_instances = max(2, math.ceil(scale_factor))
        recommended_instances = math.ceil(base_instances * (1 + self.config.scaling.growth_headroom))
        recommended_instances = max(3, recommended_instances)  # Minimum 3 for HA
        
        # Calculate CPU requirements
        cpu_per_instance = max(2, math.ceil(scale_factor * 0.5))
        cpu_per_instance = min(8, cpu_per_instance)  # Cap at 8 cores
        total_cpu = recommended_instances * cpu_per_instance
        
        # Calculate workers
        workers_per_instance = cpu_per_instance * self.config.scaling.worker_per_cpu
        total_workers = workers_per_instance * recommended_instances
        
        # Calculate memory requirements
        memory_per_user = self.config.scaling.memory_per_user_mb
        total_memory_mb = peak_concurrent * memory_per_user
        memory_per_instance_mb = total_memory_mb / recommended_instances
        memory_per_instance_gb = max(2, math.ceil(memory_per_instance_mb / 1024))
        
        # Database connections
        postgres_connections = total_workers * self.config.scaling.connections_per_worker
        postgres_pool_size = postgres_connections // 2
        
        # Redis connections
        redis_connections = postgres_connections * self.config.scaling.redis_connections_factor
        
        return ScalingRecommendations(
            fastapi_workers=total_workers,
            postgres_max_connections=postgres_connections,
            postgres_pool_size=postgres_pool_size,
            redis_max_connections=redis_connections,
            estimated_concurrent_users=peak_concurrent,
            peak_rps=peak_rps,
            recommended_instances=recommended_instances,
            memory_per_instance=f"{memory_per_instance_gb}GB",
            cpu_per_instance=f"{cpu_per_instance} cores",
            total_memory_gb=memory_per_instance_gb * recommended_instances,
            total_cpu_cores=total_cpu
        )


class BottleneckAnalyzer:
    """Analyze system bottlenecks"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
    
    def analyze(self, metrics_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze metrics for bottlenecks"""
        
        bottlenecks = []
        recommendations = []
        severity_scores = {'critical': 3, 'high': 2, 'medium': 1, 'low': 0}
        
        # Analyze container metrics
        if 'containers' in metrics_summary:
            for container_name, stats in metrics_summary['containers'].items():
                # CPU bottleneck
                avg_cpu = stats.get('avg_cpu', 0)
                if avg_cpu > self.config.thresholds.cpu_critical_threshold:
                    severity = 'critical'
                elif avg_cpu > self.config.thresholds.cpu_warning_threshold:
                    severity = 'high'
                else:
                    severity = None
                
                if severity:
                    bottlenecks.append({
                        'type': 'cpu',
                        'component': container_name,
                        'severity': severity,
                        'value': avg_cpu,
                        'threshold': self.config.thresholds.cpu_critical_threshold
                    })
                    recommendations.append({
                        'priority': severity,
                        'category': 'Performance',
                        'recommendation': f"Scale {container_name} horizontally or upgrade CPU",
                        'details': f"CPU usage at {avg_cpu:.1f}%"
                    })
                
                # Memory bottleneck
                if stats.get('memory_leak_detected', False):
                    bottlenecks.append({
                        'type': 'memory_leak',
                        'component': container_name,
                        'severity': 'critical',
                        'value': stats.get('memory_growth_rate', 0)
                    })
                    recommendations.append({
                        'priority': 'critical',
                        'category': 'Memory',
                        'recommendation': f"Fix memory leak in {container_name}",
                        'details': f"Growing at {stats.get('memory_growth_rate', 0):.2f} MB/hour"
                    })
        
        # Analyze database metrics
        if 'databases' in metrics_summary:
            for db in metrics_summary['databases']:
                utilization = db.get('utilization_percent', 0)
                if utilization > self.config.thresholds.connection_critical_threshold:
                    severity = 'critical'
                elif utilization > self.config.thresholds.connection_warning_threshold:
                    severity = 'high'
                else:
                    severity = None
                
                if severity:
                    bottlenecks.append({
                        'type': 'database_connections',
                        'component': db.get('db_type', 'unknown'),
                        'severity': severity,
                        'value': utilization,
                        'threshold': self.config.thresholds.connection_critical_threshold
                    })
                    recommendations.append({
                        'priority': severity,
                        'category': 'Database',
                        'recommendation': f"Increase {db.get('db_type', 'database')} connection pool",
                        'details': f"Utilization at {utilization:.1f}%"
                    })
        
        # Sort by severity
        bottlenecks.sort(key=lambda x: severity_scores.get(x.get('severity', 'low'), 0), reverse=True)
        recommendations.sort(key=lambda x: severity_scores.get(x.get('priority', 'low'), 0), reverse=True)
        
        return {
            'bottlenecks': bottlenecks,
            'recommendations': recommendations,
            'critical_count': len([b for b in bottlenecks if b.get('severity') == 'critical']),
            'high_count': len([b for b in bottlenecks if b.get('severity') == 'high']),
            'total_issues': len(bottlenecks)
        }
    
    def get_performance_status(self, metrics: PerformanceMetrics) -> str:
        """Determine overall performance status"""
        
        if metrics.error_rate > self.config.thresholds.acceptable_error_rate:
            return 'CRITICAL'
        elif metrics.p99_response_time > self.config.thresholds.acceptable_p99_ms:
            return 'NEEDS_IMPROVEMENT'
        elif metrics.p95_response_time > self.config.thresholds.acceptable_p95_ms:
            return 'WARNING'
        else:
            return 'GOOD'