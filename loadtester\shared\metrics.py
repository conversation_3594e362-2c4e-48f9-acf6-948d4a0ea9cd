"""
Shared metrics collection and management
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, List, Optional
from datetime import datetime
import numpy as np


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_response_time: float
    requests_per_second: float
    error_rate: float
    total_requests: int
    total_failures: int
    test_duration: float
    concurrent_users: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    def is_acceptable(self, thresholds) -> bool:
        """Check if metrics meet thresholds"""
        return (
            self.p95_response_time <= thresholds.acceptable_p95_ms and
            self.p99_response_time <= thresholds.acceptable_p99_ms and
            self.error_rate <= thresholds.acceptable_error_rate
        )


@dataclass
class ContainerMetrics:
    """Container resource metrics"""
    name: str
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    network_rx_mb: float
    network_tx_mb: float
    disk_read_mb: float
    disk_write_mb: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


@dataclass
class DatabaseMetrics:
    """Database connection metrics"""
    db_type: str
    active_connections: int
    max_connections: int
    idle_connections: int
    waiting_connections: int
    avg_query_time_ms: float
    slow_queries: int
    
    @property
    def utilization_percent(self) -> float:
        """Calculate connection pool utilization"""
        if self.max_connections == 0:
            return 0
        return (self.active_connections / self.max_connections) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['utilization_percent'] = self.utilization_percent
        return data


class MetricsCollector:
    """Centralized metrics collection"""
    
    def __init__(self):
        self.container_metrics: List[ContainerMetrics] = []
        self.database_metrics: List[DatabaseMetrics] = []
        self.performance_metrics: Optional[PerformanceMetrics] = None
        self.timestamps: List[datetime] = []
        self.custom_metrics: Dict[str, Any] = {}
    
    def add_container_metrics(self, metrics: ContainerMetrics) -> None:
        """Add container metrics"""
        self.container_metrics.append(metrics)
        self.timestamps.append(datetime.now())
    
    def add_database_metrics(self, metrics: DatabaseMetrics) -> None:
        """Add database metrics"""
        self.database_metrics.append(metrics)
    
    def set_performance_metrics(self, metrics: PerformanceMetrics) -> None:
        """Set performance metrics"""
        self.performance_metrics = metrics
    
    def add_custom_metric(self, name: str, value: Any) -> None:
        """Add custom metric"""
        if name not in self.custom_metrics:
            self.custom_metrics[name] = []
        self.custom_metrics[name].append({
            'value': value,
            'timestamp': datetime.now().isoformat()
        })
    
    def get_container_statistics(self, container_name: str = None) -> Dict[str, Any]:
        """Get statistics for container metrics"""
        if container_name:
            metrics = [m for m in self.container_metrics if m.name == container_name]
        else:
            metrics = self.container_metrics
        
        if not metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in metrics]
        memory_values = [m.memory_mb for m in metrics]
        
        return {
            'avg_cpu': np.mean(cpu_values),
            'max_cpu': np.max(cpu_values),
            'min_cpu': np.min(cpu_values),
            'std_cpu': np.std(cpu_values),
            'avg_memory_mb': np.mean(memory_values),
            'max_memory_mb': np.max(memory_values),
            'min_memory_mb': np.min(memory_values),
            'memory_growth_rate': self._calculate_growth_rate(memory_values),
            'sample_count': len(metrics)
        }
    
    def _calculate_growth_rate(self, values: List[float]) -> float:
        """Calculate growth rate using linear regression"""
        if len(values) < 2:
            return 0.0
        
        x = np.arange(len(values))
        y = np.array(values)
        
        # Simple linear regression
        n = len(x)
        xy = x * y
        x2 = x * x
        
        slope = (n * np.sum(xy) - np.sum(x) * np.sum(y)) / (n * np.sum(x2) - np.sum(x) ** 2)
        
        # Convert to hourly rate
        samples_per_hour = 3600 / (len(values) / len(self.timestamps)) if self.timestamps else 1
        return slope * samples_per_hour
    
    def detect_memory_leak(self, threshold_r2: float = 0.8) -> Dict[str, bool]:
        """Detect potential memory leaks"""
        results = {}
        
        for container in set(m.name for m in self.container_metrics):
            metrics = [m for m in self.container_metrics if m.name == container]
            if len(metrics) < 10:  # Need sufficient data points
                results[container] = False
                continue
            
            memory_values = [m.memory_mb for m in metrics]
            x = np.arange(len(memory_values))
            y = np.array(memory_values)
            
            # Calculate R-squared
            correlation_matrix = np.corrcoef(x, y)
            r_squared = correlation_matrix[0, 1] ** 2
            
            # Check if there's a strong positive correlation
            slope = np.polyfit(x, y, 1)[0]
            results[container] = r_squared > threshold_r2 and slope > 0
        
        return results
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'performance': self.performance_metrics.to_dict() if self.performance_metrics else None,
            'containers': {},
            'databases': [],
            'custom_metrics': self.custom_metrics
        }
        
        # Container statistics
        for container in set(m.name for m in self.container_metrics):
            summary['containers'][container] = self.get_container_statistics(container)
        
        # Database metrics
        for db_metric in self.database_metrics:
            summary['databases'].append(db_metric.to_dict())
        
        # Memory leak detection
        memory_leaks = self.detect_memory_leak()
        for container, has_leak in memory_leaks.items():
            if container in summary['containers']:
                summary['containers'][container]['memory_leak_detected'] = has_leak
        
        return summary