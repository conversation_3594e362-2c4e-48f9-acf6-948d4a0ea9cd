"""
Centralized configuration management
"""

import os
import json
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from pathlib import Path


@dataclass
class PerformanceThresholds:
    """Performance threshold configuration"""
    acceptable_p95_ms: int = 500
    acceptable_p99_ms: int = 1000
    acceptable_error_rate: float = 0.01
    cpu_warning_threshold: float = 70.0
    cpu_critical_threshold: float = 85.0
    memory_warning_threshold: float = 70.0
    memory_critical_threshold: float = 85.0
    connection_warning_threshold: float = 70.0
    connection_critical_threshold: float = 85.0


@dataclass
class ScalingFactors:
    """Scaling calculation factors"""
    worker_per_cpu: int = 2
    connections_per_worker: int = 50
    redis_connections_factor: int = 2
    memory_per_user_mb: int = 10
    peak_user_percentage: float = 0.3
    growth_headroom: float = 0.3


@dataclass
class DatabaseConfig:
    """Database configuration"""
    postgres_host: str = 'localhost'
    postgres_port: int = 5432
    postgres_db: str = 'testdb'
    postgres_user: str = 'postgres'
    postgres_password: str = 'postgres'
    redis_host: str = 'localhost'
    redis_port: int = 6379
    mongodb_host: str = 'localhost'
    mongodb_port: int = 27017


class Config:
    """Centralized configuration management"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # Load configuration from JSON file first
        self._load_json_config()
        
        # Load from environment variables (overrides JSON)
        self.reports_dir = Path(os.getenv('REPORTS_DIR', 'reports'))
        self.target_users = int(os.getenv('TARGET_USERS', '2000'))
        self.test_host = os.getenv('TEST_HOST', self.base_url if hasattr(self, 'base_url') else 'http://localhost:8001')
        
        # Initialize configurations
        self.thresholds = PerformanceThresholds()
        self.scaling = ScalingFactors()
        self.database = DatabaseConfig()
        
        # Load database config from environment
        self.database.postgres_host = os.getenv('POSTGRES_HOST', self.database.postgres_host)
        self.database.postgres_port = int(os.getenv('POSTGRES_PORT', self.database.postgres_port))
        self.database.postgres_db = os.getenv('POSTGRES_DB', self.database.postgres_db)
        self.database.postgres_user = os.getenv('POSTGRES_USER', self.database.postgres_user)
        self.database.postgres_password = os.getenv('POSTGRES_PASSWORD', self.database.postgres_password)
        self.database.redis_host = os.getenv('REDIS_HOST', self.database.redis_host)
        self.database.redis_port = int(os.getenv('REDIS_PORT', self.database.redis_port))
        
        # Cost estimation defaults (USD per month)
        self.cost_per_cpu = 25
        self.cost_per_gb_memory = 15
        self.cost_database_base = 300
        self.cost_redis_base = 100
        
        self._initialized = True
    
    def _load_json_config(self):
        """Load configuration from JSON file"""
        # Try multiple locations for config file
        config_paths = [
            Path('configs/config.global.json'),  # Primary location
            Path('config.global.json'),          # Root directory
            Path('config.json'),                  # Legacy location
            Path('../configs/config.global.json'), # Parent directory
        ]
        
        # Check environment variable for custom config path
        env_config = os.getenv('CONFIG_FILE')
        if env_config:
            config_paths.insert(0, Path(env_config))
        
        config_data = {}
        for config_path in config_paths:
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        print(f"Loaded configuration from: {config_path}")
                        break
                except Exception as e:
                    print(f"Error loading config from {config_path}: {e}")
        
        # Apply JSON configuration
        if config_data:
            # Basic settings
            self.base_url = config_data.get('base_url', 'http://localhost:8001')
            self.verify_ssl = config_data.get('verify_ssl', False)
            self.num_threads = config_data.get('num_threads', 8)
            
            # Timeouts
            self.timeout_connect = config_data.get('timeout_connect', 15.0)
            self.timeout_read = config_data.get('timeout_read', 90.0)
            self.timeout_write = config_data.get('timeout_write', 60.0)
            self.timeout_pool = config_data.get('timeout_pool', 15.0)
            
            # Authentication (can be overridden by environment variable)
            self.jwt_token = os.getenv('JWT_TOKEN', config_data.get('jwt_token', ''))
            
            # Endpoints configuration
            self.endpoints = config_data.get('endpoints', [])
            
            # Store raw config for reference
            self._raw_config = config_data
        else:
            # Default values if no config file found
            self.base_url = 'http://localhost:8001'
            self.verify_ssl = False
            self.num_threads = 8
            self.timeout_connect = 15.0
            self.timeout_read = 90.0
            self.timeout_write = 60.0
            self.timeout_pool = 15.0
            self.jwt_token = ''
            self.endpoints = []
            self._raw_config = {}
    
    def get_postgres_connection_string(self) -> str:
        """Get PostgreSQL connection string"""
        return (
            f"postgresql://{self.database.postgres_user}:{self.database.postgres_password}"
            f"@{self.database.postgres_host}:{self.database.postgres_port}/{self.database.postgres_db}"
        )
    
    def get_redis_url(self) -> str:
        """Get Redis URL"""
        return f"redis://{self.database.redis_host}:{self.database.redis_port}"
    
    def estimate_monthly_cost(self, cpu_cores: int, memory_gb: float, instances: int) -> Dict[str, int]:
        """Estimate monthly infrastructure cost"""
        app_cost = instances * (cpu_cores * self.cost_per_cpu + memory_gb * self.cost_per_gb_memory)
        return {
            'application_servers': int(app_cost),
            'database': self.cost_database_base,
            'cache': self.cost_redis_base,
            'total': int(app_cost + self.cost_database_base + self.cost_redis_base)
        }
    
    def get_processed_endpoints(self) -> List[Dict[str, Any]]:
        """Get endpoints with token substitution applied"""
        processed_endpoints = []
        
        for endpoint in self.endpoints:
            # Create a copy to avoid modifying original
            processed = endpoint.copy()
            
            # Process headers if present
            if 'headers' in processed:
                processed_headers = {}
                for key, value in processed['headers'].items():
                    # Replace ${jwt_token} placeholder
                    if isinstance(value, str) and '${jwt_token}' in value:
                        processed_headers[key] = value.replace('${jwt_token}', self.jwt_token)
                    else:
                        processed_headers[key] = value
                processed['headers'] = processed_headers
            
            # Add default Authorization header if JWT token is set and not already present
            if self.jwt_token and 'headers' not in processed:
                processed['headers'] = {'Authorization': f'Bearer {self.jwt_token}'}
            elif self.jwt_token and 'headers' in processed and 'Authorization' not in processed['headers']:
                processed['headers']['Authorization'] = f'Bearer {self.jwt_token}'
                
            processed_endpoints.append(processed)
            
        return processed_endpoints
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'reports_dir': str(self.reports_dir),
            'target_users': self.target_users,
            'test_host': self.test_host,
            'jwt_token': 'SET' if self.jwt_token else 'NOT_SET',
            'thresholds': {
                'p95_ms': self.thresholds.acceptable_p95_ms,
                'p99_ms': self.thresholds.acceptable_p99_ms,
                'error_rate': self.thresholds.acceptable_error_rate,
                'cpu_warning': self.thresholds.cpu_warning_threshold,
                'cpu_critical': self.thresholds.cpu_critical_threshold,
                'memory_warning': self.thresholds.memory_warning_threshold,
                'memory_critical': self.thresholds.memory_critical_threshold
            },
            'scaling': {
                'worker_per_cpu': self.scaling.worker_per_cpu,
                'connections_per_worker': self.scaling.connections_per_worker,
                'memory_per_user_mb': self.scaling.memory_per_user_mb,
                'peak_user_percentage': self.scaling.peak_user_percentage
            },
            'endpoints_count': len(self.endpoints) if hasattr(self, 'endpoints') else 0
        }