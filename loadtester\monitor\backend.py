"""
Backend monitoring for containers and databases
"""

import os
import time
import json
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from ..shared.logger import get_logger, log_error
    HAS_LOGGER = True
except ImportError:
    # Fallback if logger not available
    import logging
    HAS_LOGGER = False

try:
    import docker
    import psutil
    HAS_DOCKER = True
except ImportError:
    HAS_DOCKER = False

try:
    import psycopg2
    HAS_POSTGRES = True
except ImportError:
    HAS_POSTGRES = False

try:
    import redis
    HAS_REDIS = True
except ImportError:
    HAS_REDIS = False

from ..shared.config import Config
from ..shared.metrics import MetricsCollector, ContainerMetrics, DatabaseMetrics
from ..shared.reports import ReportGenerator


class BackendMonitor:
    """Monitors backend services using shared utilities"""
    
    def __init__(self, output_dir: str = "reports", container_names: List[str] = None):
        self.config = Config()
        self.metrics_collector = MetricsCollector()
        self.report_generator = ReportGenerator(output_dir)
        
        # Setup logging
        if HAS_LOGGER:
            self.logger = get_logger('backend')
        else:
            self.logger = logging.getLogger('backend')
            self.logger.setLevel(logging.INFO)
        
        self.container_names = container_names
        self.docker_client = docker.from_env() if HAS_DOCKER else None
        
        self.monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 2  # seconds
    
    def start_monitoring(self) -> None:
        """Start monitoring in background thread"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect container metrics
                if self.docker_client:
                    self._collect_container_metrics()
                
                # Collect database metrics
                self._collect_database_metrics()
                
                # Collect application KPIs
                self._collect_application_kpis()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                error_msg = f"Monitor error: {e}"
                print(error_msg)
                if HAS_LOGGER:
                    log_error(e, "backend_monitoring_cycle")
                else:
                    self.logger.error(error_msg)
                time.sleep(self.monitor_interval)
    
    def _collect_container_metrics(self) -> None:
        """Collect Docker container metrics"""
        if not self.docker_client:
            return
        
        containers = self.docker_client.containers.list()
        
        for container in containers:
            # Filter by name if specified
            if self.container_names and container.name not in self.container_names:
                continue
            
            try:
                stats = container.stats(stream=False)
                
                # Calculate CPU percentage
                cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                           stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                              stats['precpu_stats']['system_cpu_usage']
                cpu_percent = (cpu_delta / system_delta) * 100.0 if system_delta > 0 else 0
                
                # Memory usage
                memory_usage = stats['memory_stats'].get('usage', 0) / (1024 * 1024)  # MB
                memory_limit = stats['memory_stats'].get('limit', 1) / (1024 * 1024)
                memory_percent = (memory_usage / memory_limit * 100) if memory_limit > 0 else 0
                
                # Network I/O
                network_rx = sum(v.get('rx_bytes', 0) for v in stats['networks'].values()) / (1024 * 1024)
                network_tx = sum(v.get('tx_bytes', 0) for v in stats['networks'].values()) / (1024 * 1024)
                
                # Disk I/O
                disk_read = sum(v.get('value', 0) for v in stats.get('blkio_stats', {}).get('io_service_bytes_recursive', []) 
                               if v.get('op') == 'Read') / (1024 * 1024)
                disk_write = sum(v.get('value', 0) for v in stats.get('blkio_stats', {}).get('io_service_bytes_recursive', []) 
                                if v.get('op') == 'Write') / (1024 * 1024)
                
                # Create metrics object
                metrics = ContainerMetrics(
                    name=container.name,
                    cpu_percent=cpu_percent,
                    memory_mb=memory_usage,
                    memory_percent=memory_percent,
                    network_rx_mb=network_rx,
                    network_tx_mb=network_tx,
                    disk_read_mb=disk_read,
                    disk_write_mb=disk_write
                )
                
                self.metrics_collector.add_container_metrics(metrics)
                
            except Exception as e:
                error_msg = f"Error collecting metrics for {container.name}: {e}"
                print(error_msg)
                if HAS_LOGGER:
                    log_error(e, "container_metrics_collection", container_name=container.name)
                else:
                    self.logger.error(error_msg)
    
    def _collect_database_metrics(self) -> None:
        """Collect database connection metrics"""
        
        # PostgreSQL metrics (optional - skip if connection fails)
        if HAS_POSTGRES:
            try:
                conn_string = self.config.get_postgres_connection_string()
                conn = psycopg2.connect(conn_string)
                cursor = conn.cursor()
                
                # Get connection stats
                cursor.execute("""
                    SELECT 
                        count(*) as total,
                        count(*) filter (where state = 'active') as active,
                        count(*) filter (where state = 'idle') as idle,
                        count(*) filter (where wait_event_type is not null) as waiting
                    FROM pg_stat_activity
                    WHERE datname = current_database()
                """)
                
                row = cursor.fetchone()
                
                # Get max connections
                cursor.execute("SHOW max_connections")
                max_conn = int(cursor.fetchone()[0])
                
                metrics = DatabaseMetrics(
                    db_type='postgresql',
                    active_connections=row[1] if row else 0,
                    max_connections=max_conn,
                    idle_connections=row[2] if row else 0,
                    waiting_connections=row[3] if row else 0,
                    avg_query_time_ms=0,
                    slow_queries=0
                )
                
                self.metrics_collector.add_database_metrics(metrics)
                
                cursor.close()
                conn.close()
                
            except Exception as e:
                # PostgreSQL monitoring is optional - continue without it
                pass
        
        # Redis metrics
        if HAS_REDIS:
            try:
                r = redis.Redis(
                    host=self.config.database.redis_host,
                    port=self.config.database.redis_port,
                    decode_responses=True
                )
                
                info = r.info()
                clients = r.client_list()
                
                metrics = DatabaseMetrics(
                    db_type='redis',
                    active_connections=len(clients),
                    max_connections=int(info.get('maxclients', 10000)),
                    idle_connections=0,
                    waiting_connections=0,
                    avg_query_time_ms=0,
                    slow_queries=0
                )
                
                self.metrics_collector.add_database_metrics(metrics)
                
            except Exception as e:
                error_msg = f"Redis monitoring error: {e}"
                print(error_msg)
                if HAS_LOGGER:
                    log_error(e, "redis_metrics_collection")
                else:
                    self.logger.error(error_msg)
    
    def _collect_application_kpis(self) -> None:
        """Collect application-specific KPIs"""
        try:
            import requests
            
            # Try to get metrics from application
            response = requests.get(f"{self.config.test_host}/api/metrics", timeout=2)
            if response.status_code == 200:
                kpis = response.json()
                for key, value in kpis.items():
                    self.metrics_collector.add_custom_metric(key, value)
                    
        except Exception:
            pass  # Application metrics optional
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate monitoring report"""
        
        summary = self.metrics_collector.get_summary()
        
        # Add memory leak detection results
        memory_leaks = self.metrics_collector.detect_memory_leak()
        
        # Structure report
        report = {
            'monitoring_duration': len(self.metrics_collector.timestamps) * self.monitor_interval,
            'sample_count': len(self.metrics_collector.timestamps),
            'containers': [],
            'databases': summary.get('databases', []),
            'application_kpis': summary.get('custom_metrics', {}),
            'memory_analysis': {
                'leaks_detected': any(memory_leaks.values()),
                'containers_with_leaks': [k for k, v in memory_leaks.items() if v]
            }
        }
        
        # Add container statistics
        for container_name, stats in summary.get('containers', {}).items():
            container_data = {
                'name': container_name,
                'statistics': stats,
                'memory_leak_detected': memory_leaks.get(container_name, False)
            }
            
            # Add growth rate if leak detected
            if container_data['memory_leak_detected']:
                container_data['statistics']['memory_growth_rate_mb_per_hour'] = stats.get('memory_growth_rate', 0)
            
            report['containers'].append(container_data)
        
        return report
    
    def save_report(self, filename: str = "backend_metrics.json") -> None:
        """Save monitoring report to file"""
        report = self.generate_report()
        self.report_generator.save_json(report, filename)
        print(f"Backend metrics saved to: {self.report_generator.output_dir / filename}")