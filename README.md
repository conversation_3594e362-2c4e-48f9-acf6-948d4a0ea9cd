# Load Testing Framework

A comprehensive load testing and scaling analysis framework that combines Locust load testing with real-time backend monitoring to provide actionable infrastructure recommendations.

## Features

- 🚀 **Load Testing** - Powered by <PERSON>cust with multiple user scenarios
- 📊 **Backend Monitoring** - Real-time container and database metrics
- 📈 **Scaling Analysis** - Infrastructure sizing recommendations
- 💰 **Cost Estimation** - Monthly infrastructure cost projections
- 📝 **Comprehensive Reports** - HTML dashboards and JSON analysis

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Start services
make docker-up

# Run full analysis
make analyze-full

# View results
open reports/executive_summary.html
```

## Usage

### Basic Load Test
```bash
make quick-test  # 5 users, 30 seconds
```

### Full Analysis with Monitoring
```bash
make analyze-full  # Complete analysis with backend monitoring
```

### Custom Test
```bash
python -m loadtester.cli --users 100 --spawn-rate 10 --run-time 5m
```

## Reports

All reports are saved in `reports/`:

- `scaling_dashboard.html` - Interactive visualizations
- `executive_summary.html` - High-level summary
- `scaling_report.json` - Detailed recommendations
- `backend_metrics.json` - Container metrics

## Configuration

```bash
export TARGET_USERS=2000          # Target user capacity
export TEST_HOST=http://localhost:8000  # Application URL
export POSTGRES_HOST=localhost    # Database host
export REDIS_HOST=localhost       # Cache host
```

## Architecture

```
loadtester/
├── shared/              # Shared utilities
│   ├── config.py       # Configuration
│   ├── metrics.py      # Metrics collection
│   ├── analysis.py     # Scaling analysis
│   └── reports.py      # Report generation
├── locustfile.py       # Load test scenarios
├── integrated_runner.py # Test orchestration
├── report_analyzer.py   # Analysis engine
└── report_visualizer.py # Dashboard generation
```

## Commands

### Load Testing
```bash
make run           # Run with Locust UI
make run-headless  # Run without UI
make run-stress    # Stress test
```

### Analysis
```bash
make analyze       # Standard analysis
make analyze-full  # With backend monitoring
```

### Docker
```bash
make docker-up     # Start services
make docker-down   # Stop services
```

## Documentation

See [docs/](docs/) for detailed guides:
- [Quick Start Guide](docs/QUICK_START.md)
- [Load Testing Guide](docs/LOAD_TESTING_GUIDE.md)
- [Backend Monitoring](docs/BACKEND_MONITORING.md)

## License

MIT