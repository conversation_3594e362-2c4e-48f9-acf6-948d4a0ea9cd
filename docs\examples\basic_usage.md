# Basic Usage Examples

This guide provides practical examples for common load testing scenarios using the enhanced Locust framework.

## 🚀 Getting Started Examples

### Example 1: Simple API Load Test

```bash
# Run a basic load test with 10 users for 5 minutes
python scripts/run_load_tests.py \
  --test-type load \
  --context andoc \
  --users 10 \
  --duration 5m
```

**Expected Output:**
```
🚀 Starting load test execution...
✅ Load test completed successfully!
📊 Results: 1,250 requests, 99.2% success rate, 245ms avg response time
📄 HTML report: reports/load_test_20241221_143022_report.html
```

### Example 2: Smoke Test for New Deployment

```bash
# Quick smoke test to verify basic functionality
python scripts/run_load_tests.py \
  --test-type smoke \
  --context myapp \
  --users 1 \
  --duration 30s
```

**Use Case:** Run this after each deployment to ensure basic functionality works.

### Example 3: Stress Test to Find Breaking Point

```bash
# Stress test with aggressive load
python scripts/run_load_tests.py \
  --test-type stress \
  --context andoc \
  --users 500 \
  --spawn-rate 20 \
  --duration 10m
```

**Expected Behavior:** System should show degraded performance or failures at high load.

## 🔧 Configuration Examples

### Example 4: Environment-Specific Testing

```bash
# Test against staging environment
python scripts/run_load_tests.py \
  --test-type load \
  --context andoc \
  --environment staging \
  --users 50

# Test against production environment
python scripts/run_load_tests.py \
  --test-type load \
  --context andoc \
  --environment production \
  --users 100
```

**Configuration Files:**
```json
// configs/environments.json
{
  "staging": {
    "base_url": "https://staging-api.example.com",
    "num_users": 50,
    "verify_ssl": true
  },
  "production": {
    "base_url": "https://api.example.com", 
    "num_users": 100,
    "verify_ssl": true
  }
}
```

### Example 5: Custom Context Configuration

Create a new context for your application:

```bash
# Create new context configuration
python scripts/manage_config.py \
  --create-context ecommerce \
  --base-url https://api.ecommerce.com \
  --num-users 25
```

**Generated Configuration:**
```json
// configs/ecommerce.json
{
  "base_url": "https://api.ecommerce.com",
  "num_users": 25,
  "endpoints": [
    {
      "url": "/health",
      "method": "GET",
      "id": "health_check"
    }
  ]
}
```

## 📊 Monitoring Examples

### Example 6: Real-time Monitoring

```bash
# Run load test with real-time monitoring
python scripts/run_load_tests.py \
  --test-type load \
  --context andoc \
  --users 100 \
  --duration 15m \
  --monitor \
  --export-metrics
```

**Console Output:**
```
📊 Users: 100 | RPS: 45.2 | Failures/s: 0.8 | Avg RT: 234ms | CPU: 67.3% | Memory: 45.1%
📊 Users: 100 | RPS: 47.1 | Failures/s: 0.3 | Avg RT: 198ms | CPU: 71.2% | Memory: 46.8%
⚠️  ALERT [WARNING] high_response_time: Average response time exceeds 5 seconds
```

### Example 7: Slack Integration

```bash
# Setup Slack notifications
python scripts/setup_monitoring.py \
  --slack \
  --webhook-url https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Run test with Slack alerts
python scripts/run_load_tests.py \
  --test-type stress \
  --context andoc \
  --users 200 \
  --monitor
```

**Slack Message Example:**
```
🚨 Load Test Alert: high_error_rate
Severity: CRITICAL
Message: Error rate exceeds 10%
Time: 2024-12-21 14:30:22 UTC
```

## 🎯 Advanced Scenarios

### Example 8: Distributed Load Testing

```bash
# Run distributed test with 4 workers
python scripts/run_load_tests.py \
  --test-type load \
  --context andoc \
  --distributed \
  --workers 4 \
  --users 400 \
  --duration 20m
```

**Manual Distributed Setup:**
```bash
# Terminal 1 - Master
locust -f src/locust_tests/load_test.py \
  --master \
  --host https://api.example.com \
  --users 400 \
  --spawn-rate 20 \
  --run-time 20m \
  --headless

# Terminal 2-5 - Workers
locust -f src/locust_tests/load_test.py \
  --worker \
  --master-host localhost
```

### Example 9: Endurance Testing

```bash
# Long-duration stability test
python scripts/run_load_tests.py \
  --test-type endurance \
  --context andoc \
  --users 50 \
  --duration 4h \
  --monitor \
  --export-metrics
```

**Use Case:** Detect memory leaks, resource exhaustion, and gradual performance degradation.

### Example 10: Spike Testing

```bash
# Simulate sudden traffic spike
python scripts/run_load_tests.py \
  --test-type spike \
  --context andoc \
  --users 300 \
  --spawn-rate 100 \
  --duration 10m
```

**Behavior:** All 300 users spawn within 3 seconds, simulating viral content or breaking news.

## 🔍 PowerShell Examples (Windows)

### Example 11: PowerShell Basic Usage

```powershell
# Simple load test
.\scripts\enhanced-load-test.ps1 -TestType load -Context andoc -Users 25

# Stress test with monitoring
.\scripts\enhanced-load-test.ps1 -TestType stress -Context andoc -Users 200 -Monitor

# Test suite for multiple contexts
.\scripts\enhanced-load-test.ps1 -TestSuite -Contexts "andoc,talperftraitement"
```

### Example 12: PowerShell with Web UI

```powershell
# Run with Locust web interface
.\scripts\enhanced-load-test.ps1 -TestType load -Context andoc -WebUI

# Then open browser to http://localhost:8089
```

## 📈 Custom Test Scenarios

### Example 13: Custom User Behavior

```python
# custom_test.py
from locust_framework import AuthenticatedUser
from locust import task, between
import random

class ECommerceUser(AuthenticatedUser):
    wait_time = between(2, 8)  # Realistic user behavior
    
    def on_start(self):
        super().on_start()
        self.cart_items = []
    
    @task(5)
    def browse_products(self):
        """Browse product catalog"""
        category = random.choice(['electronics', 'clothing', 'books'])
        with self.authenticated_request("GET", f"/api/products?category={category}") as response:
            if response.status_code == 200:
                products = response.json().get('products', [])
                if products:
                    # Simulate viewing a product
                    product = random.choice(products)
                    self.view_product(product['id'])
                response.success()
            else:
                response.failure(f"Browse failed: {response.status_code}")
    
    @task(3)
    def view_product(self, product_id=None):
        """View product details"""
        if not product_id:
            product_id = random.randint(1, 1000)
        
        with self.authenticated_request("GET", f"/api/products/{product_id}") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Product view failed: {response.status_code}")
    
    @task(2)
    def add_to_cart(self):
        """Add item to shopping cart"""
        product_id = random.randint(1, 1000)
        quantity = random.randint(1, 3)
        
        payload = {
            "product_id": product_id,
            "quantity": quantity
        }
        
        with self.authenticated_request("POST", "/api/cart/add", json=payload) as response:
            if response.status_code == 201:
                self.cart_items.append(product_id)
                response.success()
            else:
                response.failure(f"Add to cart failed: {response.status_code}")
    
    @task(1)
    def checkout(self):
        """Complete purchase"""
        if not self.cart_items:
            return
        
        payload = {
            "payment_method": "credit_card",
            "shipping_address": "123 Test St, Test City, TC 12345"
        }
        
        with self.authenticated_request("POST", "/api/checkout", json=payload) as response:
            if response.status_code == 200:
                self.cart_items = []  # Clear cart after successful checkout
                response.success()
            else:
                response.failure(f"Checkout failed: {response.status_code}")
```

### Example 14: Scenario-Based Testing

```python
# scenario_test.py
from locust_framework import ScenarioUser

class BusinessScenarioUser(ScenarioUser):
    def on_start(self):
        super().on_start()
        
        # Define business scenarios
        self.scenarios = [
            {
                "name": "new_employee_onboarding",
                "weight": 3,
                "endpoints": [
                    {"url": "/hr/employee/new", "method": "POST", "data": {"name": "John Doe"}},
                    {"url": "/hr/benefits/enroll", "method": "POST", "data": {"employee_id": "{employee_id}"}},
                    {"url": "/it/account/create", "method": "POST", "data": {"employee_id": "{employee_id}"}}
                ]
            },
            {
                "name": "performance_review",
                "weight": 2,
                "endpoints": [
                    {"url": "/hr/review/start", "method": "POST", "data": {"employee_id": "{employee_id}"}},
                    {"url": "/hr/review/submit", "method": "POST", "data": {"review_id": "{review_id}", "rating": 4}}
                ]
            }
        ]
```

## 🔧 Configuration Validation Examples

### Example 15: Configuration Management

```bash
# Validate all configurations
python scripts/manage_config.py --validate-all

# Check JWT token status
python scripts/manage_config.py --validate-jwt

# Show merged configuration for debugging
python scripts/manage_config.py --show-config --context andoc --environment production

# List available contexts and environments
python scripts/manage_config.py --list-contexts
python scripts/manage_config.py --list-environments
```

**Sample Output:**
```
🔍 Validating all configurations...
✅ Global configuration is valid
✅ Context 'andoc' configuration is valid
✅ Context 'talperftraitement' configuration is valid
✅ Environment 'production' configuration is valid
🎉 All configurations are valid!
```

## 📊 Reporting Examples

### Example 16: Custom Reporting

```python
from locust_framework import ReportManager

# Initialize custom reporting
report_manager = ReportManager(report_dir="custom_reports")

# Add custom metrics
report_manager.add_custom_metric("business_transactions", 1250)
report_manager.add_custom_metric("revenue_impact", 15000.50)

# Record system metrics
report_manager.record_system_metrics(cpu_percent=65.2, memory_percent=78.1)
```

These examples cover the most common use cases and should help you get started with the enhanced Locust framework. For more advanced scenarios, refer to the API documentation and source code examples.
