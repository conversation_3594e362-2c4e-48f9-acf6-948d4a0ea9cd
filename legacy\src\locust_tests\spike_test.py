"""
Spike Test
==========

Tests the application's response to sudden increases in load.
Simulates traffic spikes that might occur during peak usage periods.
"""

import os
import json
import random
import time
from locust import task, between, events
from src.locust_framework import AuthenticatedUser


class SpikeTestUser(AuthenticatedUser):
    """User that simulates spike traffic patterns"""
    
    wait_time = between(0.5, 2.0)  # Variable wait time
    weight = 4
    
    def __init__(self, environment):
        super().__init__(environment)
        self.spike_mode = False
        self.normal_wait_time = between(2, 5)
        self.spike_wait_time = between(0.1, 0.5)
        
    def on_start(self):
        super().on_start()
        
        # Load configuration
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            self.config_data = json.load(f)
        
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
        
        # Listen for spike events
        self.setup_spike_listeners()
    
    def setup_spike_listeners(self):
        """Setup listeners for spike events"""
        @events.test_start.add_listener
        def on_test_start(environment, **kwargs):
            # Schedule spike events
            environment.runner.greenlet.spawn(self.spike_scheduler)
    
    def spike_scheduler(self):
        """Schedule spike events during the test"""
        # Wait for initial ramp-up
        time.sleep(30)
        
        while True:
            # Normal period
            self.spike_mode = False
            time.sleep(random.randint(60, 120))  # 1-2 minutes normal
            
            # Spike period
            self.spike_mode = True
            time.sleep(random.randint(30, 60))   # 30s-1min spike
    
    @task(10)
    def adaptive_messaging(self):
        """Messaging that adapts to spike conditions"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        # Adjust behavior based on spike mode
        if self.spike_mode:
            # During spike: rapid, short messages
            messages = [
                "Quick question",
                "Status?",
                "Help",
                "Info",
                "Update"
            ]
            self.wait_time = self.spike_wait_time
        else:
            # Normal: longer, detailed messages
            messages = [
                "I have a detailed question about the leave policy",
                "Can you help me understand the benefits package?",
                "I need assistance with my performance review process",
                "What are the procedures for requesting training?"
            ]
            self.wait_time = self.normal_wait_time
        
        message = random.choice(messages)
        endpoint = f"/{self.active_context}/chat/send"
        
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id,
            "spike_mode": self.spike_mode
        }
        
        name = f"spike_{self.active_context}_message" if self.spike_mode else f"normal_{self.active_context}_message"
        
        with self.authenticated_request("POST", endpoint, json=payload, name=name) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Message failed: {response.status_code}")
    
    @task(8)
    def burst_health_checks(self):
        """Health checks that burst during spikes"""
        if self.spike_mode:
            # During spike: multiple rapid health checks
            for i in range(random.randint(3, 7)):
                with self.authenticated_request("GET", "/health", 
                                              name="spike_health_burst") as response:
                    if response.status_code == 200:
                        response.success()
                    else:
                        response.failure(f"Burst health check failed: {response.status_code}")
                time.sleep(0.1)  # Very short delay between burst requests
        else:
            # Normal: single health check
            with self.authenticated_request("GET", "/health", 
                                          name="normal_health_check") as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Health check failed: {response.status_code}")
    
    @task(5)
    def session_spike_behavior(self):
        """Session operations that spike"""
        if self.spike_mode:
            # During spike: rapid session switching
            new_context = random.choice(self.contexts)
            self.active_context = new_context
            self.create_user_session(new_context)
        else:
            # Normal: check existing session
            if hasattr(self, 'session_manager') and self.session_manager.session_data.session_id:
                endpoint = f"/{self.active_context}/session/status"
                params = {"session_id": self.session_manager.session_data.session_id}
                
                with self.authenticated_request("GET", endpoint, params=params,
                                              name="session_status_check") as response:
                    if response.status_code == 200:
                        response.success()
                    else:
                        response.failure(f"Session status failed: {response.status_code}")


class PeakHourUser(AuthenticatedUser):
    """Simulates peak hour user behavior"""
    
    wait_time = between(1, 3)
    weight = 3
    
    def on_start(self):
        super().on_start()
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
        
        # Peak hour scenarios
        self.peak_scenarios = [
            "morning_rush",    # 9-10 AM
            "lunch_break",     # 12-1 PM  
            "end_of_day",      # 4-5 PM
            "deadline_crunch"  # High stress periods
        ]
        
        self.current_scenario = random.choice(self.peak_scenarios)
    
    @task(12)
    def scenario_based_requests(self):
        """Requests based on peak hour scenarios"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        scenario_messages = {
            "morning_rush": [
                "Bonjour, j'ai oublié mon mot de passe",
                "Comment accéder à mes emails?",
                "Où trouver le planning de la journée?",
                "Problème de connexion VPN"
            ],
            "lunch_break": [
                "Vérification rapide de mes congés",
                "Statut de ma demande de formation",
                "Mise à jour de mes informations",
                "Consultation rapide des bénéfices"
            ],
            "end_of_day": [
                "Soumission de ma feuille de temps",
                "Sauvegarde de mes documents",
                "Vérification des tâches de demain",
                "Déconnexion sécurisée"
            ],
            "deadline_crunch": [
                "Accès urgent aux documents",
                "Validation rapide requise",
                "Statut critique du projet",
                "Approbation d'urgence"
            ]
        }
        
        messages = scenario_messages.get(self.current_scenario, ["Generic peak hour request"])
        message = random.choice(messages)
        
        endpoint = f"/{self.active_context}/chat/send"
        payload = {
            "message": message,
            "session_id": self.session_manager.session_data.session_id,
            "scenario": self.current_scenario,
            "priority": "high" if self.current_scenario == "deadline_crunch" else "normal"
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name=f"peak_{self.current_scenario}") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Peak hour request failed: {response.status_code}")
    
    @task(6)
    def concurrent_operations(self):
        """Multiple operations happening concurrently during peak"""
        operations = [
            ("GET", "/health", {}, "peak_health"),
            ("GET", f"/{self.active_context}/chat/history", 
             {"session_id": getattr(self.session_manager.session_data, 'session_id', '')}, 
             "peak_history"),
            ("GET", "/user/profile", {}, "peak_profile"),
            ("GET", "/notifications", {}, "peak_notifications")
        ]
        
        # Execute 2-3 operations rapidly
        selected_ops = random.sample(operations, random.randint(2, 3))
        
        for method, url, params, name in selected_ops:
            with self.authenticated_request(method, url, params=params, name=name) as response:
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"Concurrent operation failed: {response.status_code}")
            
            # Very short delay between concurrent operations
            time.sleep(0.1)


class FlashCrowdUser(AuthenticatedUser):
    """Simulates flash crowd behavior (viral content, breaking news, etc.)"""
    
    wait_time = between(0.2, 1.0)  # Very active users
    weight = 2
    
    def on_start(self):
        super().on_start()
        self.contexts = ["andoc", "talperftraitement"]
        self.active_context = random.choice(self.contexts)
        self.create_user_session(self.active_context)
        
        # Flash crowd triggers
        self.crowd_triggers = [
            "breaking_news",
            "system_update",
            "policy_change",
            "emergency_notice"
        ]
        
        self.trigger = random.choice(self.crowd_triggers)
    
    @task(15)
    def viral_content_requests(self):
        """Requests for viral/trending content"""
        if not hasattr(self, 'session_manager') or not self.session_manager.session_data.session_id:
            self.create_user_session(self.active_context)
            return
        
        viral_queries = {
            "breaking_news": [
                "Quelles sont les dernières nouvelles?",
                "Mise à jour importante?",
                "Changements récents?",
                "Nouvelles annonces?"
            ],
            "system_update": [
                "Nouveau système disponible?",
                "Mise à jour des fonctionnalités?",
                "Changements dans l'interface?",
                "Nouvelles options?"
            ],
            "policy_change": [
                "Nouvelle politique RH?",
                "Changements dans les bénéfices?",
                "Mise à jour des procédures?",
                "Nouvelles règles?"
            ],
            "emergency_notice": [
                "Procédure d'urgence?",
                "Consignes de sécurité?",
                "Plan d'évacuation?",
                "Contact d'urgence?"
            ]
        }
        
        queries = viral_queries.get(self.trigger, ["Trending topic query"])
        query = random.choice(queries)
        
        endpoint = f"/{self.active_context}/chat/send"
        payload = {
            "message": query,
            "session_id": self.session_manager.session_data.session_id,
            "trigger": self.trigger,
            "crowd_behavior": True
        }
        
        with self.authenticated_request("POST", endpoint, json=payload,
                                      name=f"flash_crowd_{self.trigger}") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Flash crowd request failed: {response.status_code}")
    
    @task(8)
    def rapid_refresh_behavior(self):
        """Users rapidly refreshing for updates"""
        refresh_endpoints = [
            "/notifications",
            "/updates", 
            "/announcements",
            f"/{self.active_context}/latest"
        ]
        
        endpoint = random.choice(refresh_endpoints)
        
        with self.authenticated_request("GET", endpoint, 
                                      name="flash_crowd_refresh") as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Refresh failed: {response.status_code}")


# Define user classes for spike testing
user_classes = [SpikeTestUser, PeakHourUser, FlashCrowdUser]
