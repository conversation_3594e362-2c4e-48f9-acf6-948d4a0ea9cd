#!/usr/bin/env python3
"""
Monitoring Setup Script
======================

Sets up monitoring and alerting integrations for load testing.
Configures various monitoring systems and alert channels.
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from locust_framework.monitoring_integrations import (
    MonitoringManager, PrometheusIntegration, InfluxDBIntegration,
    SlackIntegration, EmailIntegration, WebhookIntegration
)
from locust_framework.real_time_monitor import RealTimeMonitor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Main monitoring setup function"""
    parser = argparse.ArgumentParser(
        description="Load Test Monitoring Setup",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Setup Prometheus monitoring
  python scripts/setup_monitoring.py --prometheus --pushgateway-url http://localhost:9091
  
  # Setup Slack alerts
  python scripts/setup_monitoring.py --slack --webhook-url https://hooks.slack.com/...
  
  # Setup comprehensive monitoring
  python scripts/setup_monitoring.py --config monitoring_config.json
  
  # Test monitoring integrations
  python scripts/setup_monitoring.py --test-integrations
        """
    )
    
    # Integration options
    parser.add_argument("--prometheus", action="store_true", help="Setup Prometheus integration")
    parser.add_argument("--pushgateway-url", type=str, help="Prometheus Pushgateway URL")
    parser.add_argument("--influxdb", action="store_true", help="Setup InfluxDB integration")
    parser.add_argument("--influxdb-url", type=str, help="InfluxDB URL")
    parser.add_argument("--influxdb-database", type=str, help="InfluxDB database name")
    parser.add_argument("--slack", action="store_true", help="Setup Slack integration")
    parser.add_argument("--webhook-url", type=str, help="Slack webhook URL")
    parser.add_argument("--email", action="store_true", help="Setup email integration")
    parser.add_argument("--smtp-server", type=str, help="SMTP server")
    parser.add_argument("--smtp-port", type=int, default=587, help="SMTP port")
    parser.add_argument("--email-username", type=str, help="Email username")
    parser.add_argument("--email-password", type=str, help="Email password")
    parser.add_argument("--from-email", type=str, help="From email address")
    parser.add_argument("--to-emails", type=str, help="Comma-separated to email addresses")
    
    # Configuration options
    parser.add_argument("--config", type=str, help="Monitoring configuration file")
    parser.add_argument("--save-config", type=str, help="Save configuration to file")
    parser.add_argument("--test-integrations", action="store_true", help="Test monitoring integrations")
    
    # Options
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        if args.config:
            setup_from_config(args.config)
        elif args.test_integrations:
            test_monitoring_integrations()
        else:
            setup_monitoring_integrations(args)
            
    except Exception as e:
        logger.error(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def setup_monitoring_integrations(args):
    """Setup monitoring integrations based on command line arguments"""
    logger.info("🔧 Setting up monitoring integrations...")
    
    monitoring_manager = MonitoringManager()
    config = {"integrations": {}}
    
    # Setup Prometheus integration
    if args.prometheus:
        if not args.pushgateway_url:
            args.pushgateway_url = input("Enter Prometheus Pushgateway URL: ")
        
        prometheus = PrometheusIntegration(args.pushgateway_url)
        monitoring_manager.add_integration(prometheus)
        
        config["integrations"]["prometheus"] = {
            "type": "prometheus",
            "pushgateway_url": args.pushgateway_url,
            "job_name": "locust_load_test"
        }
        
        logger.info("✅ Prometheus integration configured")
    
    # Setup InfluxDB integration
    if args.influxdb:
        if not args.influxdb_url:
            args.influxdb_url = input("Enter InfluxDB URL: ")
        if not args.influxdb_database:
            args.influxdb_database = input("Enter InfluxDB database name: ")
        
        influxdb = InfluxDBIntegration(args.influxdb_url, args.influxdb_database)
        monitoring_manager.add_integration(influxdb)
        
        config["integrations"]["influxdb"] = {
            "type": "influxdb",
            "url": args.influxdb_url,
            "database": args.influxdb_database
        }
        
        logger.info("✅ InfluxDB integration configured")
    
    # Setup Slack integration
    if args.slack:
        if not args.webhook_url:
            args.webhook_url = input("Enter Slack webhook URL: ")
        
        slack = SlackIntegration(args.webhook_url)
        monitoring_manager.add_alert_integration(slack)
        
        config["integrations"]["slack"] = {
            "type": "slack",
            "webhook_url": args.webhook_url
        }
        
        logger.info("✅ Slack integration configured")
    
    # Setup Email integration
    if args.email:
        if not args.smtp_server:
            args.smtp_server = input("Enter SMTP server: ")
        if not args.email_username:
            args.email_username = input("Enter email username: ")
        if not args.email_password:
            args.email_password = input("Enter email password: ")
        if not args.from_email:
            args.from_email = input("Enter from email address: ")
        if not args.to_emails:
            args.to_emails = input("Enter to email addresses (comma-separated): ")
        
        to_email_list = [email.strip() for email in args.to_emails.split(",")]
        
        email = EmailIntegration(
            args.smtp_server, args.smtp_port, args.email_username,
            args.email_password, args.from_email, to_email_list
        )
        monitoring_manager.add_alert_integration(email)
        
        config["integrations"]["email"] = {
            "type": "email",
            "smtp_server": args.smtp_server,
            "smtp_port": args.smtp_port,
            "username": args.email_username,
            "from_email": args.from_email,
            "to_emails": to_email_list
        }
        
        logger.info("✅ Email integration configured")
    
    # Save configuration if requested
    if args.save_config:
        with open(args.save_config, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"📄 Configuration saved to {args.save_config}")
    
    # Test integrations
    if config["integrations"]:
        logger.info("🧪 Testing integrations...")
        test_metrics = {
            "test_metric": 42,
            "active_users": 10,
            "requests_per_second": 5.5
        }
        
        results = monitoring_manager.send_metrics(test_metrics)
        for integration, success in results.items():
            status = "✅" if success else "❌"
            logger.info(f"{status} {integration}: {'Success' if success else 'Failed'}")
    
    logger.info("🎉 Monitoring setup completed!")


def setup_from_config(config_file: str):
    """Setup monitoring from configuration file"""
    logger.info(f"📋 Setting up monitoring from config: {config_file}")
    
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    monitoring_manager = MonitoringManager()
    
    for name, integration_config in config.get("integrations", {}).items():
        integration_type = integration_config.get("type")
        
        try:
            if integration_type == "prometheus":
                integration = PrometheusIntegration(
                    integration_config["pushgateway_url"],
                    integration_config.get("job_name", "locust_load_test")
                )
                monitoring_manager.add_integration(integration)
                
            elif integration_type == "influxdb":
                integration = InfluxDBIntegration(
                    integration_config["url"],
                    integration_config["database"],
                    integration_config.get("username"),
                    integration_config.get("password")
                )
                monitoring_manager.add_integration(integration)
                
            elif integration_type == "slack":
                integration = SlackIntegration(
                    integration_config["webhook_url"],
                    integration_config.get("channel")
                )
                monitoring_manager.add_alert_integration(integration)
                
            elif integration_type == "email":
                integration = EmailIntegration(
                    integration_config["smtp_server"],
                    integration_config.get("smtp_port", 587),
                    integration_config["username"],
                    integration_config.get("password", ""),
                    integration_config["from_email"],
                    integration_config["to_emails"]
                )
                monitoring_manager.add_alert_integration(integration)
                
            elif integration_type == "webhook":
                integration = WebhookIntegration(
                    integration_config["url"],
                    integration_config.get("headers", {})
                )
                monitoring_manager.add_alert_integration(integration)
                
            else:
                logger.warning(f"Unknown integration type: {integration_type}")
                
        except Exception as e:
            logger.error(f"Error setting up {name} integration: {e}")
    
    logger.info("✅ Monitoring setup from config completed!")


def test_monitoring_integrations():
    """Test existing monitoring integrations"""
    logger.info("🧪 Testing monitoring integrations...")
    
    # Try to load monitoring config
    config_files = ["monitoring_config.json", "config/monitoring.json"]
    config = None
    
    for config_file in config_files:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"📋 Loaded config from {config_file}")
            break
    
    if not config:
        logger.warning("No monitoring configuration found. Creating test setup...")
        # Create a test monitoring manager with dummy integrations
        monitoring_manager = MonitoringManager()
        
        # Add a webhook integration for testing
        webhook = WebhookIntegration("https://httpbin.org/post")
        monitoring_manager.add_alert_integration(webhook)
    else:
        # Setup from config
        monitoring_manager = MonitoringManager()
        setup_from_config_dict(monitoring_manager, config)
    
    # Test metrics
    logger.info("📊 Testing metrics...")
    test_metrics = {
        "test_timestamp": int(time.time()),
        "active_users": 25,
        "requests_per_second": 12.5,
        "avg_response_time": 250.0,
        "error_rate": 0.02,
        "cpu_usage": 45.2,
        "memory_usage": 67.8
    }
    
    results = monitoring_manager.send_metrics(test_metrics)
    for integration, success in results.items():
        status = "✅" if success else "❌"
        logger.info(f"{status} Metrics - {integration}: {'Success' if success else 'Failed'}")
    
    # Test alerts
    logger.info("🚨 Testing alerts...")
    from locust_framework.monitoring_integrations import Alert
    from datetime import datetime, timezone
    
    test_alert = Alert(
        name="test_alert",
        message="This is a test alert from the monitoring setup",
        severity="warning",
        timestamp=datetime.now(timezone.utc),
        metrics=test_metrics
    )
    
    alert_results = monitoring_manager.send_alert(test_alert)
    for integration, success in alert_results.items():
        status = "✅" if success else "❌"
        logger.info(f"{status} Alert - {integration}: {'Success' if success else 'Failed'}")
    
    logger.info("🎉 Monitoring integration testing completed!")


def setup_from_config_dict(monitoring_manager: MonitoringManager, config: Dict[str, Any]):
    """Setup monitoring manager from config dictionary"""
    for name, integration_config in config.get("integrations", {}).items():
        integration_type = integration_config.get("type")
        
        try:
            if integration_type == "prometheus":
                integration = PrometheusIntegration(
                    integration_config["pushgateway_url"],
                    integration_config.get("job_name", "locust_load_test")
                )
                monitoring_manager.add_integration(integration)
                
            elif integration_type == "influxdb":
                integration = InfluxDBIntegration(
                    integration_config["url"],
                    integration_config["database"],
                    integration_config.get("username"),
                    integration_config.get("password")
                )
                monitoring_manager.add_integration(integration)
                
            elif integration_type == "slack":
                integration = SlackIntegration(
                    integration_config["webhook_url"],
                    integration_config.get("channel")
                )
                monitoring_manager.add_alert_integration(integration)
                
            elif integration_type == "webhook":
                integration = WebhookIntegration(
                    integration_config["url"],
                    integration_config.get("headers", {})
                )
                monitoring_manager.add_alert_integration(integration)
                
        except Exception as e:
            logger.error(f"Error setting up {name} integration: {e}")


if __name__ == "__main__":
    import time
    main()
