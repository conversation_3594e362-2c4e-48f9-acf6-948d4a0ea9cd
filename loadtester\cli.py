#!/usr/bin/env python
"""
Command-line interface for LoadTester
"""
import argparse
import subprocess
import sys
import os
from pathlib import Path

def run_locust(args):
    """Run Locust with specified arguments"""
    # Find locustfile
    locustfile = Path(__file__).parent / "locustfile.py"
    if not locustfile.exists():
        locustfile = Path("loadtester/locustfile.py")
    
    cmd = ["locust", "-f", str(locustfile)]
    
    # Add host
    if args.host:
        cmd.extend(["--host", args.host])
    else:
        cmd.extend(["--host", "http://localhost:8001"])
    
    # Add users and spawn rate
    if args.users:
        cmd.extend(["-u", str(args.users)])
    
    if args.spawn_rate:
        cmd.extend(["-r", str(args.spawn_rate)])
    
    # Add run time
    if args.run_time:
        cmd.extend(["-t", args.run_time])
    
    # Headless mode
    if args.headless:
        cmd.append("--headless")
    
    # Web port
    if not args.headless:
        cmd.extend(["--web-port", str(args.web_port)])
    
    # CSV output
    if args.csv:
        cmd.extend(["--csv", args.csv])
    
    # HTML output  
    if args.html:
        cmd.extend(["--html", args.html])
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running Locust: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("Locust not found. Install with: pip install locust")
        sys.exit(1)

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="LoadTester - HTTP Load Testing Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--host",
        default="http://localhost:8001",
        help="Host to test (default: http://localhost:8001)"
    )
    
    parser.add_argument(
        "-u", "--users",
        type=int,
        default=10,
        help="Number of concurrent users (default: 10)"
    )
    
    parser.add_argument(
        "-r", "--spawn-rate",
        type=float,
        default=1,
        help="Users spawn rate (default: 1/sec)"
    )
    
    parser.add_argument(
        "-t", "--run-time",
        help="Test duration (e.g., 30s, 5m, 1h)"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run without web UI"
    )
    
    parser.add_argument(
        "--web-port",
        type=int,
        default=8089,
        help="Web UI port (default: 8089)"
    )
    
    parser.add_argument(
        "--csv",
        help="CSV output prefix"
    )
    
    parser.add_argument(
        "--html",
        help="HTML report output"
    )
    
    parser.add_argument(
        "-c", "--config",
        help="Configuration file (JSON)"
    )
    
    args = parser.parse_args()
    
    # Set config environment variable if provided
    if args.config:
        os.environ["CONFIG_FILE"] = args.config
    
    # Run Locust
    run_locust(args)

if __name__ == "__main__":
    main()