#!/usr/bin/env python3
"""
Run Analysis Pipeline
Executes the full analysis pipeline with proper module isolation
"""

import subprocess
import os
import sys
import glob
from pathlib import Path


def run_analysis_pipeline():
    """Run the complete analysis pipeline"""
    
    # Find the latest report directory
    report_dirs = glob.glob('reports/run_*')
    if not report_dirs:
        print("No report directories found in reports/")
        return 1
    
    latest_dir = max(report_dirs, key=os.path.getctime)
    print(f'Using report directory: {latest_dir}')
    
    # Define the analysis steps
    steps = [
        {
            'name': 'Generating scaling recommendations',
            'cmd': [
                sys.executable, '-m', 'loadtester.reports.analyzer',
                f'{latest_dir}/test_stats.csv',
                f'{latest_dir}/test_stats_history.csv',
                '2000',
                f'{latest_dir}/',
                '--backend-metrics', f'{latest_dir}/backend_metrics.json'
            ]
        },
        {
            'name': 'Creating visual dashboard',
            'cmd': [
                sys.executable, '-m', 'loadtester.reports.visualizer',
                f'{latest_dir}/scaling_report.json',
                f'{latest_dir}/'
            ]
        },
        {
            'name': 'Generating comprehensive analysis',
            'cmd': [
                sys.executable, '-m', 'loadtester.reports.combined',
                '--reports-dir', latest_dir
            ]
        },
        {
            'name': 'Generating Excel report',
            'cmd': [
                sys.executable, '-m', 'loadtester.reports.excel_generator',
                '--reports-dir', latest_dir
            ]
        }
    ]
    
    # Run each step in a separate subprocess
    for step in steps:
        print(f"{step['name']}...")
        try:
            # Use fresh Python interpreter for each module
            result = subprocess.run(
                step['cmd'],
                capture_output=False,  # Show output directly
                text=True,
                env=dict(os.environ, PYTHONWARNINGS='ignore'),  # Suppress all warnings
                stderr=subprocess.DEVNULL  # Suppress stderr warnings
            )
            if result.returncode != 0:
                print(f"Warning: {step['name']} returned code {result.returncode}")
        except Exception as e:
            print(f"Error in {step['name']}: {e}")
    
    # Print summary
    print(f'\nFull analysis complete! Check {latest_dir}/ directory for all outputs.')
    print('Generated reports:')
    print(f'  - {latest_dir}/test.html              : Locust HTML report')
    print(f'  - {latest_dir}/backend_metrics.json   : Backend performance metrics')
    print(f'  - {latest_dir}/scaling_report.json    : Scaling recommendations')
    print(f'  - {latest_dir}/scaling_dashboard.html : Interactive dashboard')
    print(f'  - {latest_dir}/executive_summary.html : Executive summary')
    print(f'  - {latest_dir}/comprehensive_analysis.json : Complete analysis')
    print(f'  - {latest_dir}/report_*.xlsx : Excel report with all metrics')
    
    return 0


if __name__ == '__main__':
    sys.exit(run_analysis_pipeline())