#!/usr/bin/env python3
"""
Database Load Test - Stress test database queries and connections
"""
import asyncio
import httpx
import json
import time
from datetime import datetime

class DatabaseLoadTester:
    def __init__(self, config_path: str = "config.global.json"):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        self.base_url = self.config["base_url"]
        self.headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.config['jwt_token']}"}

    async def run_database_load_test(self, concurrent_connections: int = 100):
        """Test database under concurrent load"""
        print(f"🗄️ Starting database load test with {concurrent_connections} connections...")
        
        tests = [
            ("Session Creation Load", self._test_session_creation_load),
            ("Chat History Load", self._test_chat_history_load),
            ("Concurrent Writes", self._test_concurrent_writes),
            ("Connection Pool Exhaustion", self._test_connection_pool_exhaustion)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func(concurrent_connections)
            results.append({"test_name": test_name, **result})
        
        await self._save_db_results(results)

    async def _test_session_creation_load(self, connections: int):
        """Test concurrent session creation"""
        async def create_session(user_id):
            async with httpx.AsyncClient(timeout=30.0) as client:
                try:
                    response = await client.post(
                        f"{self.base_url}/andoc/session/new",
                        headers=self.headers
                    )
                    return response.status_code == 200
                except:
                    return False
        
        tasks = [create_session(i) for i in range(connections)]
        results = await asyncio.gather(*tasks)
        success_rate = (sum(results) / len(results)) * 100
        
        return {"success_rate": success_rate, "total_sessions": connections}

if __name__ == "__main__":
    asyncio.run(DatabaseLoadTester().run_database_load_test())